package services

import (
	"fmt"
	"strings"
	"time"

	"lcheck/data"
)

// SecurityReportService 专业安全报告服务
type SecurityReportService struct{}

// NewSecurityReportService 创建安全报告服务
func NewSecurityReportService() *SecurityReportService {
	return &SecurityReportService{}
}

// GenerateSecurityReport 生成专业的Linux安全策略核查报告
func (srs *SecurityReportService) GenerateSecurityReport(task *data.ScanTask) (string, error) {
	var buffer strings.Builder
	
	// 生成HTML头部
	buffer.WriteString(srs.generateHTMLHeader(task))
	
	// 生成报告内容
	buffer.WriteString(srs.generateReportContent(task))
	
	// 生成目录导航
	buffer.WriteString(srs.generateTableOfContents())
	
	// 生成HTML尾部
	buffer.WriteString(srs.generateHTMLFooter())
	
	return buffer.String(), nil
}

// generateHTMLHeader 生成HTML头部
func (srs *SecurityReportService) generateHTMLHeader(task *data.ScanTask) string {
	// 获取主机信息用于标题
	hostInfo := "多主机"
	if len(task.Results) == 1 {
		hostInfo = fmt.Sprintf("%s_%s", task.Results[0].HostName, task.Results[0].Host)
	}

	return fmt.Sprintf(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux安全配置核查</title>
    <link rel="icon" href="#" sizes="16x16">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            position: relative;
        }

        #content {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-right: 220px;
        }

        table {
            border-collapse: collapse;
            margin-bottom: 30px;
            width: 100%%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            overflow: hidden;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 12px 15px;
            text-align: left;
            vertical-align: top;
        }

        th {
            background-color: #007BFF;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #e6f2ff;
        }

        .pass {
            color: #28a745;
            font-weight: bold;
        }

        .fail {
            color: #dc3545;
            font-weight: bold;
        }

        .warn {
            color: #ffc107;
            font-weight: bold;
        }

        h1 {
            color: #007BFF;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
            border-bottom: 3px solid #007BFF;
            padding-bottom: 15px;
        }

        h2 {
            color: #007BFF;
            border-bottom: 2px solid #007BFF;
            padding-bottom: 10px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .status-summary {
            background-color: #f8f9fa;
            border-left: 4px solid #007BFF;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 6px 6px 0;
        }

        .status-summary p {
            margin: 8px 0;
            font-size: 16px;
        }

        .command-output {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            word-break: break-all;
        }

        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        #toc {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 20px;
            max-height: 80vh;
            overflow-y: auto;
            width: 180px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        #toc h3 {
            margin-top: 0;
            color: #007BFF;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }

        #toc ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        #toc li {
            margin-bottom: 8px;
        }

        #toc a {
            text-decoration: none;
            color: #333;
            font-size: 13px;
            display: block;
            padding: 8px 10px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        #toc a:hover {
            background-color: #e6f2ff;
            color: #007BFF;
        }

        .watermark {
            position: fixed;
            color: rgba(0, 0, 0, 0.05);
            font-size: 16px;
            transform: rotate(-45deg);
            pointer-events: none;
            z-index: 1;
            user-select: none;
        }

        @media print {
            #toc {
                display: none;
            }
            #content {
                margin-right: 0;
            }
        }
    </style>
</head>

<body>
    <div id="watermark"></div>
    <div id="content">
        <h1>%s Linux安全策略核查报告</h1>
`, hostInfo)
}

// generateReportContent 生成报告主要内容
func (srs *SecurityReportService) generateReportContent(task *data.ScanTask) string {
	var buffer strings.Builder
	
	// 生成任务摘要
	buffer.WriteString(srs.generateTaskSummary(task))
	
	// 为每个主机生成详细报告
	for i, result := range task.Results {
		buffer.WriteString(srs.generateHostReport(i+1, &result))
	}
	
	return buffer.String()
}

// generateTaskSummary 生成任务摘要
func (srs *SecurityReportService) generateTaskSummary(task *data.ScanTask) string {
	// 统计总体情况
	totalHosts := len(task.Results)
	totalChecks := 0
	passedChecks := 0
	failedChecks := 0
	warningChecks := 0
	
	for _, result := range task.Results {
		totalChecks += result.TotalChecks
		passedChecks += result.PassedChecks
		failedChecks += result.FailedChecks
		warningChecks += result.WarningChecks
	}
	
	completedTime := "未完成"
	if task.CompletedAt != nil {
		completedTime = task.CompletedAt.Format("2006-01-02 15:04:05")
	}
	
	return fmt.Sprintf(`
    <div class="status-summary">
        <h2>📊 核查任务概览</h2>
        <p><strong>任务名称:</strong> %s</p>
        <p><strong>任务状态:</strong> %s</p>
        <p><strong>扫描主机:</strong> %d 台</p>
        <p><strong>完成时间:</strong> %s</p>
        <p><strong>检查统计:</strong> 总计 %d 项，<span class="pass">通过 %d 项</span>，<span class="fail">失败 %d 项</span>，<span class="warn">警告 %d 项</span></p>
    </div>
`, task.Name, task.Status, totalHosts, completedTime, totalChecks, passedChecks, failedChecks, warningChecks)
}

// generateHostReport 生成单个主机的详细报告
func (srs *SecurityReportService) generateHostReport(hostIndex int, result *data.ScanResult) string {
	var buffer strings.Builder
	
	// 主机基本信息
	buffer.WriteString(srs.generateHostBasicInfo(hostIndex, result))
	
	// 系统状态信息
	buffer.WriteString(srs.generateSystemState(result))
	
	// 安全检查结果
	buffer.WriteString(srs.generateSecurityChecks(result))
	
	return buffer.String()
}

// generateHostBasicInfo 生成主机基本信息表格
func (srs *SecurityReportService) generateHostBasicInfo(hostIndex int, result *data.ScanResult) string {
	sectionId := fmt.Sprintf("info%d", hostIndex)
	
	// 从系统信息中提取数据
	hostname := result.HostName
	osInfo := "Linux"
	if result.SystemInfo != nil {
		if result.SystemInfo.OS != "" {
			osInfo = result.SystemInfo.OS
		}
		if result.SystemInfo.OSVersion != "" {
			osInfo += " " + result.SystemInfo.OSVersion
		}
	}
	
	return fmt.Sprintf(`
    <h2 id="%s">🖥️ 主机 %d: %s (%s) - 服务器基本信息</h2>
    <table>
        <thead>
        <tr>
            <th>主机名</th>
            <th>IP地址</th>
            <th>操作系统</th>
            <th>扫描状态</th>
            <th>安全得分</th>
            <th>检查项统计</th>
            <th>扫描时间</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>%s</td>
            <td>%s</td>
            <td>%s</td>
            <td class="%s">%s</td>
            <td>%d/%d</td>
            <td>通过:%d 失败:%d 警告:%d</td>
            <td>%s</td>
        </tr>
        </tbody>
    </table>
`, sectionId, hostIndex, hostname, result.Host, hostname, result.Host, osInfo, 
   srs.getStatusClass(result.Status), result.Status, result.TotalScore, result.MaxScore,
   result.PassedChecks, result.FailedChecks, result.WarningChecks,
   result.EndTime.Format("2006-01-02 15:04:05"))
}

// generateSystemState 生成系统状态信息
func (srs *SecurityReportService) generateSystemState(result *data.ScanResult) string {
	return fmt.Sprintf(`
    <h2 id="systemstate">📈 系统状态</h2>
    <table>
        <thead>
        <tr>
            <th>扫描开始时间</th>
            <th>扫描结束时间</th>
            <th>扫描耗时</th>
            <th>连接状态</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>%s</td>
            <td>%s</td>
            <td>%v</td>
            <td class="%s">%s</td>
        </tr>
        </tbody>
    </table>
`, result.StartTime.Format("2006-01-02 15:04:05"), 
   result.EndTime.Format("2006-01-02 15:04:05"),
   result.Duration,
   srs.getStatusClass(result.Status), result.Status)
}

// generateSecurityChecks 生成安全检查结果表格
func (srs *SecurityReportService) generateSecurityChecks(result *data.ScanResult) string {
	var buffer strings.Builder

	// 如果没有检查结果，显示连接失败信息
	if len(result.CheckResults) == 0 {
		buffer.WriteString(srs.generateConnectionFailureTable(result))
		return buffer.String()
	}

	// 创建安全表格服务
	securityService := NewSecurityTableService()

	// 生成专业的安全检查表格
	buffer.WriteString(securityService.GeneratePasswordPolicyTable(result.CheckResults))
	buffer.WriteString(securityService.GenerateSSHConfigTable(result.CheckResults))
	buffer.WriteString(securityService.GeneratePortStatusTable(result.CheckResults))

	// 按类别分组其他检查结果
	categoryMap := make(map[string][]data.BaselineCheckResult)
	for _, check := range result.CheckResults {
		// 跳过已经在专业表格中显示的检查项
		if check.ID == "PWD_POLICY" || check.ID == "SSH_CONFIG" || check.ID == "PORT_STATUS" {
			continue
		}
		categoryMap[check.Category] = append(categoryMap[check.Category], check)
	}

	// 为其他类别生成表格
	for category, checks := range categoryMap {
		if len(checks) > 0 {
			buffer.WriteString(srs.generateCategoryTable(category, checks))
		}
	}

	return buffer.String()
}

// generateCategoryTable 生成特定类别的检查结果表格
func (srs *SecurityReportService) generateCategoryTable(category string, checks []data.BaselineCheckResult) string {
	// 生成锚点ID
	anchorId := strings.ToLower(strings.ReplaceAll(category, " ", ""))

	var buffer strings.Builder
	buffer.WriteString(fmt.Sprintf(`
    <h2 id="%s">🔍 %s</h2>
    <table>
        <thead>
        <tr>
            <th>检查项目</th>
            <th>检查结果</th>
            <th>风险等级</th>
            <th>检查命令</th>
            <th>命令输出</th>
            <th>详情说明</th>
            <th>解决方案</th>
        </tr>
        </thead>
        <tbody>
`, anchorId, category))

	for _, check := range checks {
		statusClass := srs.getStatusClass(check.Status)
		riskClass := srs.getRiskClass(check.Risk)

		// 处理命令输出，限制长度
		output := check.Output
		if len(output) > 200 {
			output = output[:200] + "..."
		}

		buffer.WriteString(fmt.Sprintf(`
        <tr>
            <td><strong>%s</strong><br><small>ID: %s</small></td>
            <td class="%s">%s</td>
            <td class="%s">%s</td>
            <td><code>%s</code></td>
            <td><div class="command-output">%s</div></td>
            <td>%s</td>
            <td>%s</td>
        </tr>
`, check.CheckName, check.ID, statusClass, check.Status, riskClass, check.Risk,
   check.Command, output, check.Details, check.Solution))
	}

	buffer.WriteString(`
        </tbody>
    </table>
`)

	return buffer.String()
}

// generateConnectionFailureTable 生成连接失败信息表格
func (srs *SecurityReportService) generateConnectionFailureTable(result *data.ScanResult) string {
	return fmt.Sprintf(`
    <h2 id="connection">❌ 连接状态</h2>
    <table>
        <thead>
        <tr>
            <th>主机信息</th>
            <th>连接状态</th>
            <th>错误信息</th>
            <th>建议处理</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td>%s (%s:%s)</td>
            <td class="fail">连接失败</td>
            <td>%s</td>
            <td>检查网络连通性、SSH服务状态、防火墙配置和认证信息</td>
        </tr>
        </tbody>
    </table>
`, result.HostName, result.Host, "22", result.Error)
}

// generateTableOfContents 生成目录导航（已集成到HTML尾部）
func (srs *SecurityReportService) generateTableOfContents() string {
	return ""
}

// generateHTMLFooter 生成HTML尾部
func (srs *SecurityReportService) generateHTMLFooter() string {
	return fmt.Sprintf(`
        <div style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
            <p>报告生成时间: %s</p>
            <p>生成工具: Lcheck v3.2.0 - Linux远程基线安全检查工具</p>
        </div>
    </div>

    <div id="toc">
        <h3>📋 目录导航</h3>
        <ul>
            <li><a href="#info1">🖥️ 服务器基本信息</a></li>
            <li><a href="#systemstate">📈 系统状态</a></li>
            <li><a href="#pwquality">🔐 密码复杂度策略</a></li>
            <li><a href="#sshd">🔑 SSH安全配置</a></li>
            <li><a href="#port">🌐 端口开放状态</a></li>
            <li><a href="#连接性检查">🔗 连接性检查</a></li>
            <li><a href="#ssh安全">🛡️ SSH安全</a></li>
            <li><a href="#用户账户">👤 用户账户</a></li>
            <li><a href="#文件权限">📁 文件权限</a></li>
            <li><a href="#网络安全">🌐 网络安全</a></li>
            <li><a href="#防火墙">🔥 防火墙</a></li>
            <li><a href="#系统信息">ℹ️ 系统信息</a></li>
        </ul>
    </div>

    <script>
        // 平滑滚动到锚点
        document.querySelectorAll('#toc a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            createWatermarks();
        });
    </script>
</body>
</html>`, time.Now().Format("2006-01-02 15:04:05"))
}

// getStatusClass 获取状态对应的CSS类
func (srs *SecurityReportService) getStatusClass(status string) string {
	switch status {
	case "通过", "成功":
		return "pass"
	case "失败":
		return "fail"
	case "警告":
		return "warn"
	default:
		return ""
	}
}

// getRiskClass 获取风险等级对应的CSS类
func (srs *SecurityReportService) getRiskClass(risk string) string {
	switch risk {
	case "高", "严重":
		return "fail"
	case "中", "中等":
		return "warn"
	case "低":
		return "pass"
	default:
		return ""
	}
}
