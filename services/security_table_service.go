package services

import (
	"fmt"
	"strings"

	"lcheck/data"
)

// SecurityTableService 安全检查表格服务
type SecurityTableService struct{}

// NewSecurityTableService 创建安全检查表格服务
func NewSecurityTableService() *SecurityTableService {
	return &SecurityTableService{}
}

// GeneratePasswordPolicyTable 生成密码复杂度策略表格
func (sts *SecurityTableService) GeneratePasswordPolicyTable(checkResults []data.BaselineCheckResult) string {
	pwdPolicyResult := sts.findCheckResult(checkResults, "PWD_POLICY")
	if pwdPolicyResult == nil {
		return ""
	}

	// 获取实际配置值或错误信息
	minlen := "未配置"
	dcredit := "未配置"
	ucredit := "未配置"
	lcredit := "未配置"
	ocredit := "未配置"
	minclass := "未配置"
	maxrepeat := "未配置"
	maxsequence := "未配置"

	if pwdPolicyResult.Metadata != nil {
		if val, exists := pwdPolicyResult.Metadata["minlen"]; exists && val != "" {
			minlen = val
		}
		if val, exists := pwdPolicyResult.Metadata["dcredit"]; exists && val != "" {
			dcredit = val
		}
		if val, exists := pwdPolicyResult.Metadata["ucredit"]; exists && val != "" {
			ucredit = val
		}
		if val, exists := pwdPolicyResult.Metadata["lcredit"]; exists && val != "" {
			lcredit = val
		}
		if val, exists := pwdPolicyResult.Metadata["ocredit"]; exists && val != "" {
			ocredit = val
		}
		if val, exists := pwdPolicyResult.Metadata["minclass"]; exists && val != "" {
			minclass = val
		}
		if val, exists := pwdPolicyResult.Metadata["maxrepeat"]; exists && val != "" {
			maxrepeat = val
		}
		if val, exists := pwdPolicyResult.Metadata["maxsequence"]; exists && val != "" {
			maxsequence = val
		}
	}

	// 如果有错误信息，显示错误信息
	if pwdPolicyResult.Error != "" || strings.Contains(pwdPolicyResult.Output, "No such file") {
		errorMsg := pwdPolicyResult.Output
		if errorMsg == "" {
			errorMsg = pwdPolicyResult.Error
		}
		minlen = errorMsg
		dcredit = errorMsg
		ucredit = errorMsg
		lcredit = errorMsg
		ocredit = errorMsg
		minclass = errorMsg
		maxrepeat = errorMsg
		maxsequence = errorMsg
	}

	return fmt.Sprintf(`
        <h2 id="pwquality">🔐 密码复杂度策略 (/etc/security/pwquality.conf)</h2>
        <table>
            <thead>
                <tr>
                    <th style="width: 10%%;">最小长度</th>
                    <th style="width: 10%%;">数字字符位数</th>
                    <th style="width: 10%%;">大写字母位数</th>
                    <th style="width: 10%%;">小写字母位数</th>
                    <th style="width: 10%%;">特殊符号位数</th>
                    <th style="width: 10%%;">最小类别数量</th>
                    <th style="width: 12%%;">重复字符最大位数</th>
                    <th style="width: 12%%;">连续重复字符位数</th>
                    <th style="width: 16%%;">建议配置</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td style="font-size: 12px;">
                        在/etc/security/pwquality.conf配置文件中:<br/>
                        <code>minlen = 8</code><br/>
                        <code>minclass = 4</code><br/>
                        <code>maxrepeat = 2</code><br/>
                        <code>maxsequence = 2</code>
                    </td>
                </tr>
            </tbody>
        </table>
`, minlen, dcredit, ucredit, lcredit, ocredit, minclass, maxrepeat, maxsequence)
}

// GenerateSSHConfigTable 生成SSH配置信息表格
func (sts *SecurityTableService) GenerateSSHConfigTable(checkResults []data.BaselineCheckResult) string {
	sshConfigResult := sts.findCheckResult(checkResults, "SSH_CONFIG")
	if sshConfigResult == nil {
		return ""
	}

	// 获取实际SSH配置值
	permitRootLogin := "yes"
	passwordAuth := "yes"
	emptyPasswords := "no"
	pubkeyAuth := "yes"
	protocol := "2"
	maxAuthTries := "6"

	if sshConfigResult.Metadata != nil {
		if val, exists := sshConfigResult.Metadata["permitRootLogin"]; exists {
			permitRootLogin = val
		}
		if val, exists := sshConfigResult.Metadata["passwordAuthentication"]; exists {
			passwordAuth = val
		}
		if val, exists := sshConfigResult.Metadata["permitEmptyPasswords"]; exists {
			emptyPasswords = val
		}
		if val, exists := sshConfigResult.Metadata["pubkeyAuthentication"]; exists {
			pubkeyAuth = val
		}
		if val, exists := sshConfigResult.Metadata["protocol"]; exists {
			protocol = val
		}
		if val, exists := sshConfigResult.Metadata["maxAuthTries"]; exists {
			maxAuthTries = val
		}
	}

	// 转换为布尔值显示
	rootLoginDisplay := "false"
	if permitRootLogin == "yes" {
		rootLoginDisplay = "true"
	}

	passwordAuthDisplay := "false"
	if passwordAuth == "yes" {
		passwordAuthDisplay = "true"
	}

	emptyPasswordsDisplay := "true"
	if emptyPasswords == "no" {
		emptyPasswordsDisplay = "false"
	}

	pubkeyAuthDisplay := "false"
	if pubkeyAuth == "yes" {
		pubkeyAuthDisplay = "true"
	}

	protocolDisplay := "SSHV" + protocol

	return fmt.Sprintf(`
        <h2 id="sshd">🔑 SSH安全配置信息 (sshd_config)</h2>
        <table>
            <thead>
                <tr>
                    <th style="width: 12%%;">Root登录</th>
                    <th style="width: 12%%;">密码验证</th>
                    <th style="width: 12%%;">空密码认证</th>
                    <th style="width: 12%%;">密钥登录</th>
                    <th style="width: 10%%;">协议版本</th>
                    <th style="width: 12%%;">最大尝试次数</th>
                    <th style="width: 30%%;">建议配置</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong class="%s">%s</strong></td>
                    <td><strong class="%s">%s</strong></td>
                    <td><strong class="%s">%s</strong></td>
                    <td><strong class="%s">%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td><strong>%s</strong></td>
                    <td style="font-size: 12px;">
                        在/etc/ssh/sshd_config配置文件中:<br/>
                        <code>PermitRootLogin no</code><br/>
                        <code>PasswordAuthentication yes</code><br/>
                        <code>PermitEmptyPasswords no</code><br/>
                        <code>PubkeyAuthentication yes</code><br/>
                        <code>Protocol 2</code><br/>
                        <code>MaxAuthTries 3</code>
                    </td>
                </tr>
            </tbody>
        </table>
`,
	sts.getSSHStatusClass(rootLoginDisplay), rootLoginDisplay,
	sts.getSSHStatusClass(passwordAuthDisplay), passwordAuthDisplay,
	sts.getSSHStatusClass(emptyPasswordsDisplay), emptyPasswordsDisplay,
	sts.getSSHStatusClass(pubkeyAuthDisplay), pubkeyAuthDisplay,
	protocolDisplay, maxAuthTries)
}

// GeneratePortStatusTable 生成端口开放状态表格
func (sts *SecurityTableService) GeneratePortStatusTable(checkResults []data.BaselineCheckResult) string {
	portResult := sts.findCheckResult(checkResults, "PORT_STATUS")
	if portResult == nil {
		return ""
	}

	var buffer strings.Builder
	buffer.WriteString(`
        <h2 id="port">🌐 端口开放状态</h2>
        <table>
            <thead>
                <tr>
                    <th style="width: 15%;">协议</th>
                    <th style="width: 15%;">状态</th>
                    <th style="width: 35%;">监听地址</th>
                    <th style="width: 35%;">进程信息</th>
                </tr>
            </thead>
            <tbody>
`)

	// 解析端口详情并显示实际结果
	if portResult.Metadata != nil {
		if portDetails, exists := portResult.Metadata["portDetails"]; exists {
			ports := strings.Split(portDetails, ";")
			for _, port := range ports {
				if port == "" {
					continue
				}
				parts := strings.Split(port, "|")
				if len(parts) >= 4 {
					protocol := parts[0]
					state := parts[1]
					address := parts[2]
					process := parts[3]
					buffer.WriteString(fmt.Sprintf(`
                <tr>
                    <td><strong>%s</strong></td>
                    <td><span class="pass">%s</span></td>
                    <td><code>%s</code></td>
                    <td>%s</td>
                </tr>
`, protocol, state, address, process))
				}
			}
		}
	}

	// 如果没有端口信息，显示提示
	if portResult.Metadata == nil || portResult.Metadata["portDetails"] == "" {
		buffer.WriteString(`
                <tr>
                    <td colspan="4" style="text-align: center; color: #666; font-style: italic;">
                        未发现监听端口或无法获取端口信息
                    </td>
                </tr>
`)
	}

	buffer.WriteString(`
            </tbody>
        </table>
`)

	return buffer.String()
}

// findCheckResult 查找特定ID的检查结果
func (sts *SecurityTableService) findCheckResult(checkResults []data.BaselineCheckResult, checkID string) *data.BaselineCheckResult {
	for i := range checkResults {
		if checkResults[i].ID == checkID {
			return &checkResults[i]
		}
	}
	return nil
}

// getSSHStatusClass 获取SSH配置状态对应的CSS类
func (sts *SecurityTableService) getSSHStatusClass(value string) string {
	switch value {
	case "true":
		return "fail"  // root登录为true是不安全的
	case "false":
		return "pass"  // root登录为false是安全的
	default:
		return ""
	}
}
