package services

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"lcheck/data"
)

// ExportOptions 导出选项
type ExportOptions struct {
	Format     string // "html", "csv", "json"
	Content    string // "全部内容", "仅失败项目", "仅摘要信息"
	IncludeRaw bool   // 是否包含原始输出
}

// ExportService 导出服务
type ExportService struct {
	storage data.Storage
}

// NewExportService 创建导出服务
func NewExportService(storage data.Storage) *ExportService {
	return &ExportService{
		storage: storage,
	}
}

// ExportTask 导出任务结果
func (es *ExportService) ExportTask(task *data.ScanTask, options ExportOptions) (string, error) {
	switch options.Format {
	case "html":
		return es.exportToHTML(task, options)
	case "csv":
		return es.exportToCSV(task, options)
	case "json":
		return es.exportToJSON(task, options)
	default:
		return "", fmt.Errorf("不支持的导出格式: %s", options.Format)
	}
}

// SaveExportFile 保存导出文件
func (es *ExportService) SaveExportFile(filePath, content string) error {
	// 确保exports目录存在
	exportDir := filepath.Dir(filePath)
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}

// ExportTaskAsSecurityReport 导出任务为专业的安全检查报告
func (es *ExportService) ExportTaskAsSecurityReport(task *data.ScanTask) (string, error) {
	htmlService := NewSecurityReportService()
	return htmlService.GenerateSecurityReport(task)
}

// GenerateFileName 生成导出文件名
func (es *ExportService) GenerateFileName(task *data.ScanTask, format string) string {
	timestamp := time.Now().Format("20060102_150405")
	taskName := strings.ReplaceAll(task.Name, " ", "_")
	return fmt.Sprintf("%s_%s.%s", taskName, timestamp, format)
}

// FormatFileSize 格式化文件大小
func (es *ExportService) FormatFileSize(size int) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// findCheckResult 查找特定ID的检查结果
func (es *ExportService) findCheckResult(checkResults []data.BaselineCheckResult, checkID string) *data.BaselineCheckResult {
	for i := range checkResults {
		if checkResults[i].ID == checkID {
			return &checkResults[i]
		}
	}
	return nil
}

// escapeCSV 转义CSV字段中的特殊字符
func (es *ExportService) escapeCSV(field string) string {
	if strings.Contains(field, ",") || strings.Contains(field, "\"") || strings.Contains(field, "\n") {
		field = strings.ReplaceAll(field, "\"", "\"\"")
		field = "\"" + field + "\""
	}
	return field
}

// exportToHTML 导出为HTML格式
func (es *ExportService) exportToHTML(task *data.ScanTask, options ExportOptions) (string, error) {
	htmlService := NewHTMLTemplateService()
	securityService := NewSecurityTableService()

	var buffer strings.Builder

	// HTML头部
	buffer.WriteString(htmlService.GenerateHTMLHeader())

	// 任务摘要
	buffer.WriteString(htmlService.GenerateTaskSummary(task))

	// 主机结果
	for i, result := range task.Results {
		// 主机头部
		buffer.WriteString(htmlService.GenerateHostHeader(i, &result))

		// 得分信息
		buffer.WriteString(htmlService.GenerateScoreInfo(&result))

		// 安全检查表格 - 按模板格式：实际结果 + 建议配置
		buffer.WriteString(securityService.GeneratePasswordPolicyTable(result.CheckResults))
		buffer.WriteString(securityService.GenerateSSHConfigTable(result.CheckResults))
		buffer.WriteString(securityService.GeneratePortStatusTable(result.CheckResults))

		// 检查结果表格
		buffer.WriteString(htmlService.GenerateCheckResultsTable(result.CheckResults, options.Content))

		buffer.WriteString(`
            </div>
        </div>
`)
	}

	// HTML尾部
	buffer.WriteString(htmlService.GenerateHTMLFooter())

	return buffer.String(), nil
}

// exportToCSV 导出为CSV格式
func (es *ExportService) exportToCSV(task *data.ScanTask, options ExportOptions) (string, error) {
	var buffer strings.Builder

	// CSV头部 - 包含更详细的信息
	if options.IncludeRaw {
		buffer.WriteString("主机名,主机地址,检查项ID,检查项名称,类别,结果,风险等级,得分,检查命令,命令输出,详情说明,解决方案,检查时间,耗时(ms)\n")
	} else {
		buffer.WriteString("主机名,主机地址,检查项,类别,结果,风险等级,得分,详情,解决方案\n")
	}

	// 数据行
	for _, result := range task.Results {
		for _, check := range result.CheckResults {
			if options.Content == "仅失败项目" && check.Status == "通过" {
				continue
			}

			if options.IncludeRaw {
				// 包含原始输出的详细格式
				buffer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%s,%s,%d,%s,%s,%s,%s,%s,%.2f\n",
					es.escapeCSV(result.HostName),
					es.escapeCSV(result.Host),
					es.escapeCSV(check.ID),
					es.escapeCSV(check.CheckName),
					es.escapeCSV(check.Category),
					es.escapeCSV(check.Status),
					es.escapeCSV(check.Risk),
					check.Score,
					es.escapeCSV(check.Command),
					es.escapeCSV(check.Output),
					es.escapeCSV(check.Details),
					es.escapeCSV(check.Solution),
					check.CheckedAt.Format("2006-01-02 15:04:05"),
					float64(check.Duration.Nanoseconds())/1000000.0))
			} else {
				// 简化格式
				buffer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%s,%d,%s,%s\n",
					es.escapeCSV(result.HostName),
					es.escapeCSV(result.Host),
					es.escapeCSV(check.CheckName),
					es.escapeCSV(check.Category),
					es.escapeCSV(check.Status),
					es.escapeCSV(check.Risk),
					check.Score,
					es.escapeCSV(check.Details),
					es.escapeCSV(check.Solution)))
			}
		}
	}

	return buffer.String(), nil
}

// exportToJSON 导出为JSON格式
func (es *ExportService) exportToJSON(task *data.ScanTask, options ExportOptions) (string, error) {
	// 这里可以实现JSON导出逻辑
	return fmt.Sprintf(`{"task": "%s", "status": "not implemented"}`, task.Name), nil
}

// ExportHostsToCSV 导出主机信息为CSV格式（只包含核心连接信息）
func (es *ExportService) ExportHostsToCSV(hosts []data.HostInfo) string {
	var buffer strings.Builder

	// CSV头部 - 只包含核心连接信息
	buffer.WriteString("主机名,主机地址,端口,用户名,密码\n")

	// 数据行
	for _, host := range hosts {
		buffer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s\n",
			es.escapeCSV(host.Name),
			es.escapeCSV(host.Host),
			es.escapeCSV(host.Port),
			es.escapeCSV(host.Username),
			es.escapeCSV(host.Password)))
	}

	return buffer.String()
}

// ExportGroupsToCSV 导出主机组信息为CSV格式（只包含核心信息，支持批量导入）
func (es *ExportService) ExportGroupsToCSV(groups []data.HostGroup) string {
	var buffer strings.Builder

	// CSV头部 - 只包含核心信息
	buffer.WriteString("主机组名,主机组描述,主机名,主机地址,端口,用户名,密码\n")

	// 数据行 - 每台主机一行，包含主机组信息
	for _, group := range groups {
		// 如果主机组没有主机，也要导出主机组信息
		if len(group.Hosts) == 0 {
			buffer.WriteString(fmt.Sprintf("%s,%s,,,,,\n",
				es.escapeCSV(group.Name),
				es.escapeCSV(group.Description)))
		} else {
			// 为每台主机生成一行，包含主机组信息
			for _, host := range group.Hosts {
				buffer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%s,%s\n",
					es.escapeCSV(group.Name),
					es.escapeCSV(group.Description),
					es.escapeCSV(host.Name),
					es.escapeCSV(host.Host),
					es.escapeCSV(host.Port),
					es.escapeCSV(host.Username),
					es.escapeCSV(host.Password)))
			}
		}
	}

	return buffer.String()
}

// GenerateHostFileName 生成主机导出文件名
func (es *ExportService) GenerateHostFileName() string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("hosts_export_%s.csv", timestamp)
}

// GenerateGroupFileName 生成主机组导出文件名
func (es *ExportService) GenerateGroupFileName() string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("groups_export_%s.csv", timestamp)
}

// IsGroupTask 判断任务是否为主机组任务
func (es *ExportService) IsGroupTask(task *data.ScanTask) (bool, *data.HostGroup) {
	// 加载所有主机组
	groups, err := es.storage.LoadGroups()
	if err != nil {
		return false, nil
	}

	// 检查任务中的主机ID是否都来自同一个主机组
	for _, group := range groups {
		groupHostIDs := make(map[string]bool)
		for _, host := range group.Hosts {
			groupHostIDs[host.ID] = true
		}

		// 检查任务的所有主机ID是否都在这个主机组中
		allInGroup := true
		for _, hostID := range task.HostIDs {
			if !groupHostIDs[hostID] {
				allInGroup = false
				break
			}
		}

		// 如果任务的主机ID都在这个主机组中，且数量匹配，则认为是主机组任务
		if allInGroup && len(task.HostIDs) == len(group.Hosts) {
			return true, &group
		}
	}

	return false, nil
}

// ExportGroupTaskAsHTML 导出主机组任务为HTML格式（每个主机单独的报告文件）
func (es *ExportService) ExportGroupTaskAsHTML(task *data.ScanTask, group *data.HostGroup) error {
	timestamp := time.Now().Format("20060102_150405")

	// 创建以主机组名命名的文件夹
	groupFolderName := fmt.Sprintf("%s_%s",
		strings.ReplaceAll(group.Name, " ", "_"), timestamp)
	groupFolderPath := filepath.Join("exports", groupFolderName)

	// 确保文件夹存在
	if err := os.MkdirAll(groupFolderPath, 0755); err != nil {
		return fmt.Errorf("创建主机组文件夹失败: %v", err)
	}

	// 为每个主机生成单独的报告
	htmlService := NewSecurityReportService()

	for _, result := range task.Results {
		// 创建单主机任务用于生成报告
		singleHostTask := &data.ScanTask{
			ID:          task.ID + "_" + result.HostName,
			Name:        fmt.Sprintf("%s - %s", group.Name, result.HostName),
			Status:      task.Status,
			Progress:    task.Progress,
			HostIDs:     []string{result.HostName}, // 使用主机名作为标识
			Template:    task.Template,
			CreatedAt:   task.CreatedAt,
			StartedAt:   task.StartedAt,
			CompletedAt: task.CompletedAt,
			Results:     []data.ScanResult{result}, // 只包含当前主机的结果
			Error:       task.Error,
		}

		// 生成HTML报告内容
		htmlContent, err := htmlService.GenerateSecurityReport(singleHostTask)
		if err != nil {
			return fmt.Errorf("生成主机 %s 的报告失败: %v", result.HostName, err)
		}

		// 生成文件名：主机名_IP地址_时间戳.html
		hostFileName := fmt.Sprintf("%s_%s_Linux安全策略核查_%s.html",
			strings.ReplaceAll(result.HostName, " ", "_"),
			strings.ReplaceAll(result.Host, ".", "_"),
			timestamp)

		hostFilePath := filepath.Join(groupFolderPath, hostFileName)

		// 保存文件
		if err := es.SaveExportFile(hostFilePath, htmlContent); err != nil {
			return fmt.Errorf("保存主机 %s 的报告文件失败: %v", result.HostName, err)
		}
	}

	return nil
}

// ImportHostsFromCSV 从CSV格式导入主机信息
func (es *ExportService) ImportHostsFromCSV(csvContent string) ([]data.HostInfo, error) {
	lines := strings.Split(csvContent, "\n")
	if len(lines) < 2 {
		return nil, fmt.Errorf("CSV文件格式错误：至少需要标题行和一行数据")
	}

	// 验证标题行
	expectedHeader := "主机名,主机地址,端口,用户名,密码"
	actualHeader := strings.TrimSpace(lines[0])
	actualHeader = strings.ReplaceAll(actualHeader, "\r", "") // 移除Windows行尾符
	if actualHeader != expectedHeader {
		return nil, fmt.Errorf("CSV文件格式错误：标题行应为 '%s'，实际为 '%s'", expectedHeader, actualHeader)
	}

	var hosts []data.HostInfo
	for i, line := range lines[1:] {
		line = strings.TrimSpace(line)
		if line == "" {
			continue // 跳过空行
		}

		// 解析CSV行
		fields := es.parseCSVLine(line)
		if len(fields) != 5 {
			return nil, fmt.Errorf("第%d行格式错误：应包含5个字段，实际%d个", i+2, len(fields))
		}

		// 创建主机信息
		host := data.HostInfo{
			ID:       "", // 导入时会重新生成ID
			Name:     fields[0],
			Host:     fields[1],
			Port:     fields[2],
			Username: fields[3],
			Password: fields[4],
		}

		hosts = append(hosts, host)
	}

	return hosts, nil
}

// ImportGroupsFromCSV 从CSV格式导入主机组信息（简化格式）
func (es *ExportService) ImportGroupsFromCSV(csvContent string) ([]data.HostGroup, error) {
	lines := strings.Split(csvContent, "\n")
	if len(lines) < 2 {
		return nil, fmt.Errorf("CSV文件格式错误：至少需要标题行和一行数据")
	}

	// 验证标题行
	expectedHeader := "主机组名,主机组描述,主机名,主机地址,端口,用户名,密码"
	actualHeader := strings.TrimSpace(lines[0])
	actualHeader = strings.ReplaceAll(actualHeader, "\r", "") // 移除Windows行尾符
	if actualHeader != expectedHeader {
		return nil, fmt.Errorf("CSV文件格式错误：标题行应为 '%s'，实际为 '%s'", expectedHeader, actualHeader)
	}

	// 使用map来收集主机组数据
	groupMap := make(map[string]*data.HostGroup)

	for i, line := range lines[1:] {
		line = strings.TrimSpace(line)
		if line == "" {
			continue // 跳过空行
		}

		// 解析CSV行
		fields := es.parseCSVLine(line)
		if len(fields) != 7 {
			return nil, fmt.Errorf("第%d行格式错误：应包含7个字段，实际%d个", i+2, len(fields))
		}

		groupName := fields[0]

		// 如果主机组不存在，创建新的主机组
		if _, exists := groupMap[groupName]; !exists {
			groupMap[groupName] = &data.HostGroup{
				ID:          "", // 导入时会重新生成ID
				Name:        groupName,
				Description: fields[1],
				Hosts:       []data.HostInfo{},
			}
		}

		// 如果有主机信息，添加到主机组中
		if fields[2] != "" { // 主机名不为空
			// 创建主机信息
			host := data.HostInfo{
				ID:       "", // 导入时会重新生成ID
				Name:     fields[2],
				Host:     fields[3],
				Port:     fields[4],
				Username: fields[5],
				Password: fields[6],
			}

			// 添加主机到主机组
			groupMap[groupName].Hosts = append(groupMap[groupName].Hosts, host)
		}
	}

	// 转换map为slice
	var groups []data.HostGroup
	for _, group := range groupMap {
		groups = append(groups, *group)
	}

	return groups, nil
}

// parseCSVLine 解析CSV行，处理引号和转义
func (es *ExportService) parseCSVLine(line string) []string {
	var fields []string
	var current strings.Builder
	inQuotes := false

	for i, char := range line {
		switch char {
		case '"':
			if inQuotes && i+1 < len(line) && line[i+1] == '"' {
				// 双引号转义
				current.WriteRune('"')
				i++ // 跳过下一个引号
			} else {
				inQuotes = !inQuotes
			}
		case ',':
			if inQuotes {
				current.WriteRune(char)
			} else {
				fields = append(fields, current.String())
				current.Reset()
			}
		default:
			current.WriteRune(char)
		}
	}

	// 添加最后一个字段
	fields = append(fields, current.String())

	return fields
}
