package checkers

import (
	"fmt"
	"strings"

	"lcheck/data"
)

// SystemCollector 系统信息收集器 - 专门负责系统信息收集
type SystemCollector struct {
	name string
}

// NewSystemCollector 创建系统信息收集器
func NewSystemCollector() *SystemCollector {
	return &SystemCollector{
		name: "系统信息收集器",
	}
}

// GetName 获取收集器名称
func (sc *SystemCollector) GetName() string {
	return sc.name
}

// GetCollectors 获取系统信息收集项
func (sc *SystemCollector) GetCollectors() []data.BaselineCheck {
	return []data.BaselineCheck{
		{
			ID:          "SYS_INFO",
			Name:        "服务器基本信息收集",
			Category:    "信息收集",
			Description: "收集服务器基本信息",
			Risk:        "信息",
			CheckFunc: func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return sc.collectSystemInfo(client.(SSHExecutor), host)
			},
		},
		{
			ID:          "PORT_STATUS",
			Name:        "端口开放状态收集",
			Category:    "网络安全",
			Description: "收集系统端口开放状态和监听服务",
			Risk:        "信息",
			CheckFunc: func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return sc.collectPortStatus(client.(SSHExecutor), host)
			},
		},
	}
}

// Collect 执行特定信息收集
func (sc *SystemCollector) Collect(sshClient SSHExecutor, host data.HostInfo, collectorID string) (*data.BaselineCheckResult, error) {
	collectors := sc.GetCollectors()
	for _, collector := range collectors {
		if collector.ID == collectorID {
			result := collector.CheckFunc(sshClient, host)
			result.CheckName = collector.Name
			result.Category = collector.Category
			result.Description = collector.Description
			result.Risk = collector.Risk
			return &result, nil
		}
	}
	return nil, fmt.Errorf("信息收集项 %s 不存在", collectorID)
}

// CollectAll 执行所有信息收集
func (sc *SystemCollector) CollectAll(sshClient SSHExecutor, host data.HostInfo) ([]data.BaselineCheckResult, error) {
	var results []data.BaselineCheckResult
	collectors := sc.GetCollectors()

	for _, collector := range collectors {
		result := collector.CheckFunc(sshClient, host)
		result.CheckName = collector.Name
		result.Category = collector.Category
		result.Description = collector.Description
		result.Risk = collector.Risk
		results = append(results, result)
	}

	return results, nil
}

// collectSystemInfo 收集服务器基本信息
func (sc *SystemCollector) collectSystemInfo(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		ID:      "SYS_INFO",
		Command: "echo '=== HOSTNAME ==='; hostname 2>/dev/null || echo 'Unknown'; echo '=== ARCH ==='; uname -m 2>/dev/null || echo 'Unknown'; echo '=== KERNEL ==='; uname -r 2>/dev/null || echo 'Unknown'; echo '=== CPU ==='; (lscpu 2>/dev/null | grep -i 'model name' | head -1 | cut -d: -f2 | sed 's/^[ \\t]*//') || (cat /proc/cpuinfo 2>/dev/null | grep -i 'model name' | head -1 | cut -d: -f2 | sed 's/^[ \\t]*//') || echo 'Unknown'; echo '=== CPUCOUNT ==='; (nproc 2>/dev/null) || (cat /proc/cpuinfo 2>/dev/null | grep -c '^processor') || echo '1'; echo '=== MEMORY ==='; (free -h 2>/dev/null | grep -i mem) || (cat /proc/meminfo 2>/dev/null | grep -E '^(MemTotal|MemFree):' | head -2) || echo 'Unknown'; echo '=== OS ==='; (cat /etc/os-release 2>/dev/null | grep PRETTY_NAME | cut -d= -f2 | tr -d '\"') || (cat /etc/redhat-release 2>/dev/null) || (cat /etc/debian_version 2>/dev/null | sed 's/^/Debian /') || (uname -s) || echo 'Unknown'; echo '=== UPTIME ==='; uptime 2>/dev/null | sed 's/.*up //' | sed 's/,.*//' || echo 'Unknown'",
	}

	output, err := sshClient.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output
	result.Status = "信息收集"
	result.Score = 100

	if result.Metadata == nil {
		result.Metadata = make(map[string]string)
	}

	// 解析各项信息
	sections := strings.Split(output, "=== ")
	for _, section := range sections {
		if strings.Contains(section, "HOSTNAME") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["hostname"] = strings.TrimSpace(lines[1])
			}
		} else if strings.Contains(section, "ARCH") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["arch"] = strings.TrimSpace(lines[1])
			}
		} else if strings.Contains(section, "CPU") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				cpuInfo := strings.TrimSpace(lines[1])
				if strings.Contains(cpuInfo, "Model name:") {
					parts := strings.Split(cpuInfo, ":")
					if len(parts) > 1 {
						result.Metadata["cpuModel"] = strings.TrimSpace(parts[1])
					}
				}
			}
		} else if strings.Contains(section, "CPUCOUNT") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["cpuCount"] = strings.TrimSpace(lines[1])
				result.Metadata["cpuCores"] = strings.TrimSpace(lines[1])
			}
		} else if strings.Contains(section, "MEMORY") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				memInfo := strings.TrimSpace(lines[1])
				fields := strings.Fields(memInfo)
				if len(fields) > 1 {
					result.Metadata["memory"] = fields[1] + "G"
				}
			}
		} else if strings.Contains(section, "HARDWARE") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["hardware"] = strings.TrimSpace(lines[1])
			}
		} else if strings.Contains(section, "OS") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				osInfo := strings.TrimSpace(lines[1])
				if strings.Contains(osInfo, "PRETTY_NAME=") {
					parts := strings.Split(osInfo, "=")
					if len(parts) > 1 {
						result.Metadata["osVersion"] = strings.Trim(parts[1], "\"")
					}
				}
			}
		} else if strings.Contains(section, "NETWORK") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["networkTest"] = strings.TrimSpace(lines[1])
			}
		}
	}

	result.Details = "服务器基本信息收集完成"
	return result
}

// collectPortStatus 收集端口开放状态
func (sc *SystemCollector) collectPortStatus(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		ID:      "PORT_STATUS",
		Command: "(netstat -tuln 2>/dev/null | grep -i listen | head -20) || (ss -tuln 2>/dev/null | grep -i listen | head -20) || (lsof -i -P -n 2>/dev/null | grep -i listen | head -20) || echo 'No listening ports found'",
	}

	output, err := sshClient.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if result.Metadata == nil {
		result.Metadata = make(map[string]string)
	}

	if strings.Contains(output, "No listening ports found") {
		result.Status = "警告"
		result.Score = 50
		result.Details = "未发现监听端口或无法获取端口信息"
		result.Metadata["portDetails"] = ""
	} else {
		result.Status = "信息收集"
		result.Score = 100
		result.Details = "端口状态信息收集完成"

		// 解析端口信息
		var portDetails []string
		lines := strings.Split(output, "\n")

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" || !strings.Contains(line, "LISTEN") {
				continue
			}

			// 解析netstat或ss输出
			fields := strings.Fields(line)
			if len(fields) >= 4 {
				protocol := fields[0]  // tcp/udp
				address := fields[3]   // 监听地址

				// 获取进程信息（如果可用）
				process := "unknown"
				if len(fields) > 6 {
					process = fields[6]
				}

				// 格式化端口详情：协议|状态|监听地址|进程信息
				portDetail := fmt.Sprintf("%s|LISTEN|%s|%s", protocol, address, process)
				portDetails = append(portDetails, portDetail)
			}
		}

		// 将端口详情存储到metadata中，用分号分隔
		result.Metadata["portDetails"] = strings.Join(portDetails, ";")
	}

	return result
}
