package components

import (
	"fmt"
	"strconv"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
	"lcheck/services"
)

// CreateTaskDialog 创建任务对话框
type CreateTaskDialog struct {
	dialog       dialog.Dialog
	window       fyne.Window
	hostService  *services.HostService
	taskService  *services.TaskService
	onTaskCreate func(*data.ScanTask)

	// UI组件
	nameEntry       *widget.Entry
	modeSelect      *widget.RadioGroup
	hostList        *widget.List
	groupList       *widget.List
	concurrencyEntry *widget.Entry
	
	// 动态容器
	targetContainer *fyne.Container
	
	// 数据
	hosts  []data.HostInfo
	groups []data.HostGroup
	selectedHosts  []string
	selectedGroups []string
}

// ShowCreateTaskDialog 显示创建任务对话框
func ShowCreateTaskDialog(hostService *services.HostService, taskService *services.TaskService,
	onTaskCreate func(*data.ScanTask), parent fyne.Window) {

	fmt.Println("=== ShowCreateTaskDialog 开始 ===")

	// 使用 defer 来捕获可能的 panic
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("ShowCreateTaskDialog 中捕获到 panic: %v\n", r)
		}
	}()

	// 参数验证
	if parent == nil {
		fmt.Println("错误：parent window 为 nil")
		return
	}
	fmt.Println("✓ Parent window 不为 nil")

	if hostService == nil {
		fmt.Println("错误：hostService 为 nil")
		ShowErrorDialog("初始化错误", fmt.Errorf("主机服务未初始化"), parent)
		return
	}
	fmt.Println("✓ HostService 不为 nil")

	if taskService == nil {
		fmt.Println("错误：taskService 为 nil")
		ShowErrorDialog("初始化错误", fmt.Errorf("任务服务未初始化"), parent)
		return
	}
	fmt.Println("✓ TaskService 不为 nil")

	fmt.Println("创建 CreateTaskDialog 实例...")
	dialog := &CreateTaskDialog{
		window:       parent,
		hostService:  hostService,
		taskService:  taskService,
		onTaskCreate: onTaskCreate,
		selectedHosts:  make([]string, 0),
		selectedGroups: make([]string, 0),
	}
	fmt.Println("✓ CreateTaskDialog 实例创建成功")

	// 安全地加载数据和构建对话框
	fmt.Println("开始加载数据...")
	if err := dialog.loadDataSafely(); err != nil {
		fmt.Printf("数据加载失败: %v\n", err)
		ShowErrorDialog("数据加载失败", err, parent)
		return
	}
	fmt.Println("✓ 数据加载成功")

	fmt.Println("开始构建对话框...")
	dialog.buildDialog()
	fmt.Println("✓ 对话框构建成功")

	fmt.Println("显示对话框...")
	dialog.dialog.Show()
	fmt.Println("✓ 对话框显示完成")
}

// loadData 加载主机和主机组数据
func (d *CreateTaskDialog) loadData() {
	// 加载主机列表
	hosts, err := d.hostService.LoadHosts()
	if err != nil {
		d.hosts = make([]data.HostInfo, 0)
	} else {
		d.hosts = hosts
	}

	// 加载主机组列表
	groups, err := d.hostService.LoadGroups()
	if err != nil {
		d.groups = make([]data.HostGroup, 0)
	} else {
		d.groups = groups
	}
}

// loadDataSafely 安全地加载数据，返回错误信息
func (d *CreateTaskDialog) loadDataSafely() error {
	fmt.Println("  开始加载主机列表...")

	// 加载主机列表
	hosts, err := d.hostService.LoadHosts()
	if err != nil {
		fmt.Printf("  加载主机列表失败: %v\n", err)
		return fmt.Errorf("加载主机列表失败: %v", err)
	}
	d.hosts = hosts
	fmt.Printf("  ✓ 加载了 %d 个主机\n", len(hosts))

	fmt.Println("  开始加载主机组列表...")

	// 加载主机组列表
	groups, err := d.hostService.LoadGroups()
	if err != nil {
		fmt.Printf("  加载主机组列表失败: %v\n", err)
		return fmt.Errorf("加载主机组列表失败: %v", err)
	}
	d.groups = groups
	fmt.Printf("  ✓ 加载了 %d 个主机组\n", len(groups))

	return nil
}

// buildDialog 构建对话框
func (d *CreateTaskDialog) buildDialog() {
	fmt.Println("  开始构建对话框UI...")

	// 安全检查
	if d.window == nil {
		fmt.Println("  错误：window 为 nil，无法创建对话框")
		return
	}
	fmt.Println("  ✓ Window 检查通过")

	// 任务名称输入
	fmt.Println("  创建任务名称输入框...")
	d.nameEntry = widget.NewEntry()
	d.nameEntry.SetPlaceHolder("请输入任务名称")
	fmt.Println("  ✓ 任务名称输入框创建成功")

	// 模式选择
	fmt.Println("  创建模式选择组件...")
	d.modeSelect = widget.NewRadioGroup([]string{"主机模式", "主机组"}, d.onModeChanged)
	d.modeSelect.SetSelected("主机模式") // 默认选择主机模式
	fmt.Println("  ✓ 模式选择组件创建成功")

	// 创建动态目标容器
	fmt.Println("  创建动态目标容器...")
	d.targetContainer = container.NewVBox()
	fmt.Println("  ✓ 动态目标容器创建成功")

	// 初始显示主机列表
	fmt.Println("  显示主机列表...")
	d.showHostList()
	fmt.Println("  ✓ 主机列表显示成功")
	
	// 创建表单布局
	form := container.NewVBox(
		// 任务名称行
		container.NewBorder(nil, nil, 
			widget.NewLabel("任务名称"), nil,
			d.nameEntry,
		),
		
		// 模式选择行
		container.NewBorder(nil, nil,
			widget.NewLabel("选择主机或主机组模式"), nil,
			d.modeSelect,
		),
		
		// 动态目标选择区域
		d.targetContainer,
	)
	
	// 创建按钮
	saveButton := widget.NewButton("保存", d.onSave)
	cancelButton := widget.NewButton("取消", d.onCancel)
	
	buttonContainer := container.NewHBox(
		saveButton,
		cancelButton,
	)
	
	// 创建完整内容
	content := container.NewVBox(
		form,
		widget.NewSeparator(),
		container.NewCenter(buttonContainer),
	)
	
	// 创建对话框
	d.dialog = dialog.NewCustom("创建任务", "", content, d.window)
	d.dialog.Resize(fyne.NewSize(500, 400))
}

// onModeChanged 模式切换处理
func (d *CreateTaskDialog) onModeChanged(selected string) {
	d.targetContainer.RemoveAll()
	
	switch selected {
	case "主机模式":
		d.showHostList()
	case "主机组":
		d.showGroupList()
	}
	
	d.targetContainer.Refresh()
}

// showHostList 显示主机列表
func (d *CreateTaskDialog) showHostList() {
	fmt.Println("    开始显示主机列表...")

	if len(d.hosts) == 0 {
		fmt.Println("    没有主机，显示空提示")
		emptyLabel := widget.NewLabel("暂无主机")
		d.targetContainer.Add(container.NewBorder(nil, nil,
			widget.NewLabel("主机列表/主机组列表"), nil,
			emptyLabel,
		))
		return
	}

	fmt.Printf("    创建主机列表，共 %d 个主机\n", len(d.hosts))

	// 创建主机列表
	d.hostList = widget.NewList(
		func() int {
			return len(d.hosts)
		},
		func() fyne.CanvasObject {
			check := widget.NewCheck("", nil)
			label := widget.NewLabel("")
			return container.NewHBox(check, label)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			// 添加安全检查
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("    主机列表更新时发生 panic: %v\n", r)
				}
			}()

			if id >= len(d.hosts) {
				return
			}

			host := d.hosts[id]

			// 安全的类型断言
			hostContainer, ok := obj.(*fyne.Container)
			if !ok || len(hostContainer.Objects) < 2 {
				fmt.Println("    类型断言失败或对象数量不足")
				return
			}

			check, ok := hostContainer.Objects[0].(*widget.Check)
			if !ok {
				fmt.Println("    Check 组件类型断言失败")
				return
			}

			label, ok := hostContainer.Objects[1].(*widget.Label)
			if !ok {
				fmt.Println("    Label 组件类型断言失败")
				return
			}

			label.SetText(fmt.Sprintf("%s (%s)", host.Name, host.Host))

			// 设置选中状态
			isSelected := d.isHostSelected(host.ID)
			check.SetChecked(isSelected)

			// 设置选择回调
			check.OnChanged = func(checked bool) {
				d.toggleHostSelection(host.ID, checked)
			}
		},
	)
	
	d.hostList.Resize(fyne.NewSize(400, 200))
	
	d.targetContainer.Add(container.NewBorder(nil, nil,
		widget.NewLabel("主机列表/主机组列表"), nil,
		container.NewScroll(d.hostList),
	))
}

// showGroupList 显示主机组列表
func (d *CreateTaskDialog) showGroupList() {
	if len(d.groups) == 0 {
		emptyLabel := widget.NewLabel("暂无主机组")
		d.targetContainer.Add(container.NewBorder(nil, nil,
			widget.NewLabel("主机列表/主机组列表"), nil,
			emptyLabel,
		))
		return
	}
	
	// 创建主机组列表
	d.groupList = widget.NewList(
		func() int {
			return len(d.groups)
		},
		func() fyne.CanvasObject {
			check := widget.NewCheck("", nil)
			label := widget.NewLabel("")
			return container.NewHBox(check, label)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			// 添加安全检查
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("    主机组列表更新时发生 panic: %v\n", r)
				}
			}()

			if id >= len(d.groups) {
				return
			}

			group := d.groups[id]

			// 安全的类型断言
			groupContainer, ok := obj.(*fyne.Container)
			if !ok || len(groupContainer.Objects) < 2 {
				fmt.Println("    主机组容器类型断言失败或对象数量不足")
				return
			}

			check, ok := groupContainer.Objects[0].(*widget.Check)
			if !ok {
				fmt.Println("    主机组 Check 组件类型断言失败")
				return
			}

			label, ok := groupContainer.Objects[1].(*widget.Label)
			if !ok {
				fmt.Println("    主机组 Label 组件类型断言失败")
				return
			}

			label.SetText(fmt.Sprintf("%s (%d个主机)", group.Name, len(group.Hosts)))

			// 设置选中状态
			isSelected := d.isGroupSelected(group.ID)
			check.SetChecked(isSelected)

			// 设置选择回调
			check.OnChanged = func(checked bool) {
				d.toggleGroupSelection(group.ID, checked)
			}
		},
	)
	
	d.groupList.Resize(fyne.NewSize(400, 150))
	
	// 并发设置
	d.concurrencyEntry = widget.NewEntry()
	d.concurrencyEntry.SetText("5") // 默认并发数
	d.concurrencyEntry.SetPlaceHolder("并发数")
	
	concurrencyContainer := container.NewBorder(nil, nil,
		widget.NewLabel("选择主机组才有并发设置"), nil,
		d.concurrencyEntry,
	)
	
	d.targetContainer.Add(container.NewBorder(nil, nil,
		widget.NewLabel("主机列表/主机组列表"), nil,
		container.NewScroll(d.groupList),
	))
	
	d.targetContainer.Add(concurrencyContainer)
}

// 辅助方法
func (d *CreateTaskDialog) isHostSelected(hostID string) bool {
	for _, id := range d.selectedHosts {
		if id == hostID {
			return true
		}
	}
	return false
}

func (d *CreateTaskDialog) isGroupSelected(groupID string) bool {
	for _, id := range d.selectedGroups {
		if id == groupID {
			return true
		}
	}
	return false
}

func (d *CreateTaskDialog) toggleHostSelection(hostID string, selected bool) {
	if selected {
		if !d.isHostSelected(hostID) {
			d.selectedHosts = append(d.selectedHosts, hostID)
		}
	} else {
		for i, id := range d.selectedHosts {
			if id == hostID {
				d.selectedHosts = append(d.selectedHosts[:i], d.selectedHosts[i+1:]...)
				break
			}
		}
	}
}

func (d *CreateTaskDialog) toggleGroupSelection(groupID string, selected bool) {
	if selected {
		if !d.isGroupSelected(groupID) {
			d.selectedGroups = append(d.selectedGroups, groupID)
		}
	} else {
		for i, id := range d.selectedGroups {
			if id == groupID {
				d.selectedGroups = append(d.selectedGroups[:i], d.selectedGroups[i+1:]...)
				break
			}
		}
	}
}

// onSave 保存任务
func (d *CreateTaskDialog) onSave() {
	// 验证输入
	taskName := d.nameEntry.Text
	if taskName == "" {
		ShowErrorDialog("输入错误", fmt.Errorf("请输入任务名称"), d.window)
		return
	}

	selectedMode := d.modeSelect.Selected
	var hostIDs []string
	var concurrency int = 1

	if selectedMode == "主机模式" {
		if len(d.selectedHosts) == 0 {
			ShowErrorDialog("选择错误", fmt.Errorf("请至少选择一个主机"), d.window)
			return
		}
		hostIDs = d.selectedHosts
	} else if selectedMode == "主机组" {
		if len(d.selectedGroups) == 0 {
			ShowErrorDialog("选择错误", fmt.Errorf("请至少选择一个主机组"), d.window)
			return
		}

		// 获取主机组中的所有主机ID
		for _, groupID := range d.selectedGroups {
			for _, group := range d.groups {
				if group.ID == groupID {
					for _, host := range group.Hosts {
						hostIDs = append(hostIDs, host.ID)
					}
					break
				}
			}
		}

		// 解析并发数
		if d.concurrencyEntry != nil && d.concurrencyEntry.Text != "" {
			var err error
			concurrency, err = strconv.Atoi(d.concurrencyEntry.Text)
			if err != nil || concurrency < 1 {
				ShowErrorDialog("输入错误", fmt.Errorf("并发数必须是大于0的整数"), d.window)
				return
			}
		}
	}

	if len(hostIDs) == 0 {
		ShowErrorDialog("选择错误", fmt.Errorf("没有找到可用的主机"), d.window)
		return
	}

	// 创建任务
	task, err := d.taskService.CreateTask(taskName, hostIDs, concurrency)
	if err != nil {
		ShowErrorDialog("创建任务失败", err, d.window)
		return
	}

	// 保存任务
	err = d.taskService.SaveTask(task)
	if err != nil {
		ShowErrorDialog("保存任务失败", err, d.window)
		return
	}

	// 回调通知任务创建成功
	if d.onTaskCreate != nil {
		d.onTaskCreate(task)
	}

	// 关闭对话框
	d.dialog.Hide()

	// 显示成功消息
	ShowSuccessDialog("创建成功", fmt.Sprintf("任务 '%s' 创建成功", taskName), d.window)
}

// onCancel 取消操作
func (d *CreateTaskDialog) onCancel() {
	d.dialog.Hide()
}
