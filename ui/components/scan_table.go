package components

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// ScanTable 扫描任务列表组件 - 使用List组件实现专业的任务列表
type ScanTable struct {
	container       fyne.CanvasObject
	taskList        *widget.List
	tasks           []data.ScanTask
	selectedIndex   int
	onSelection     func(*data.ScanTask)
	onDoubleClick   func(*data.ScanTask)
}

// NewScanTable 创建新的扫描任务列表
func NewScanTable() *ScanTable {
	st := &ScanTable{
		tasks:         make([]data.ScanTask, 0),
		selectedIndex: -1,
	}

	// 创建List组件
	st.taskList = widget.NewList(
		// Length函数：返回任务数量
		func() int {
			return len(st.tasks)
		},
		// CreateItem函数：创建列表项模板
		func() fyne.CanvasObject {
			// 创建任务项模板
			nameLabel := widget.NewLabel("任务名称")
			nameLabel.TextStyle = fyne.TextStyle{Bold: true}

			// 创建进度标签，显示百分比
			progressLabel := widget.NewLabel("0%")
			progressLabel.Alignment = fyne.TextAlignCenter

			statusLabel := widget.NewLabel("状态")
			statusLabel.Alignment = fyne.TextAlignCenter

			// 创建进度标签容器，固定宽度
			progressContainer := container.NewWithoutLayout(progressLabel)
			progressContainer.Resize(fyne.NewSize(60, 30))

			// 创建状态标签容器，固定宽度确保文字显示完整
			statusContainer := container.NewWithoutLayout(statusLabel)
			statusContainer.Resize(fyne.NewSize(100, 30))

			// 创建中间区域，用于分隔任务名称和右侧内容
			middleSpace := container.NewWithoutLayout()
			middleSpace.Resize(fyne.NewSize(50, 30))

			// 创建右侧容器：进度百分比 + 状态，使用HBox确保水平排列
			rightContainer := container.NewHBox(
				progressContainer,
				statusContainer,
			)

			// 创建整体布局：任务名称 | 中间空间 | 进度百分比+状态
			itemContainer := container.NewBorder(
				nil, nil,
				nameLabel,                    // 左侧：任务名称
				rightContainer,               // 右侧：进度百分比+状态
				middleSpace,                  // 中间：空间分隔
			)

			// 添加内边距
			return container.NewPadded(itemContainer)
		},
		// UpdateItem函数：更新列表项内容
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(st.tasks) {
				return
			}

			task := &st.tasks[id]
			paddedContainer := obj.(*fyne.Container)
			itemContainer := paddedContainer.Objects[0].(*fyne.Container)

			// Border容器的布局：Objects[0]=左侧, Objects[1]=中间, Objects[2]=右侧
			// 但实际上Border容器的Objects顺序可能不同，我们需要安全地获取组件
			var nameLabel *widget.Label
			var statusLabel *widget.Label
			var progressLabel *widget.Label

			// 递归查找组件，使用计数器来区分相同类型的标签
			labelCount := 0
			var findComponents func(container *fyne.Container)
			findComponents = func(container *fyne.Container) {
				for _, obj := range container.Objects {
					switch v := obj.(type) {
					case *widget.Label:
						// 根据文本样式和顺序判断是哪个标签
						if v.TextStyle.Bold {
							nameLabel = v
						} else if v.Alignment == fyne.TextAlignCenter {
							labelCount++
							if labelCount == 1 {
								// 第一个居中标签是进度标签
								progressLabel = v
							} else if labelCount == 2 {
								// 第二个居中标签是状态标签
								statusLabel = v
							}
						}
					case *fyne.Container:
						// 递归查找子容器
						findComponents(v)
					}
				}
			}

			findComponents(itemContainer)

			// 安全检查并更新内容
			if nameLabel != nil {
				nameLabel.SetText(task.Name)
			}
			if statusLabel != nil {
				statusLabel.SetText(task.Status)

				// 设置状态颜色
				switch task.Status {
				case "运行中":
					statusLabel.Importance = widget.MediumImportance
				case "已完成":
					statusLabel.Importance = widget.SuccessImportance
				case "失败":
					statusLabel.Importance = widget.DangerImportance
				default:
					statusLabel.Importance = widget.LowImportance
				}
			}
			if progressLabel != nil {
				// 显示百分比进度
				progressText := fmt.Sprintf("%.0f%%", task.Progress*100)
				progressLabel.SetText(progressText)
			}
		},
	)

	// 设置选择回调
	st.taskList.OnSelected = func(id widget.ListItemID) {
		st.selectedIndex = id
		if st.onSelection != nil && id < len(st.tasks) {
			st.onSelection(&st.tasks[id])
		}
	}

	// 设置取消选择回调
	st.taskList.OnUnselected = func(id widget.ListItemID) {
		st.selectedIndex = -1
	}

	// 设置主容器
	st.container = st.taskList

	return st
}

// GetContainer 获取容器
func (st *ScanTable) GetContainer() fyne.CanvasObject {
	return st.container
}

// SetTasks 设置任务列表
func (st *ScanTable) SetTasks(tasks []data.ScanTask) {
	st.tasks = tasks
	st.selectedIndex = -1
	st.taskList.Refresh()
}

// SetOnSelection 设置选择回调
func (st *ScanTable) SetOnSelection(callback func(*data.ScanTask)) {
	st.onSelection = callback
}

// SetOnDoubleClick 设置双击回调
func (st *ScanTable) SetOnDoubleClick(callback func(*data.ScanTask)) {
	st.onDoubleClick = callback
}

// GetSelectedTask 获取当前选中的任务
func (st *ScanTable) GetSelectedTask() *data.ScanTask {
	if st.selectedIndex >= 0 && st.selectedIndex < len(st.tasks) {
		return &st.tasks[st.selectedIndex]
	}
	return nil
}

// SelectTask 选择指定的任务
func (st *ScanTable) SelectTask(taskID string) {
	for i, task := range st.tasks {
		if task.ID == taskID {
			st.taskList.Select(i)
			break
		}
	}
}

// ClearSelection 清除选择
func (st *ScanTable) ClearSelection() {
	st.taskList.UnselectAll()
}

// RefreshTasks 刷新任务显示
func (st *ScanTable) RefreshTasks() {
	st.taskList.Refresh()
}
