package ui

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"lcheck/core"
	"lcheck/data"
	"lcheck/services"
	"lcheck/ui/components"
)

// ScanTab 扫描标签页 - 模块化门面，委托给专门的UI组件
type ScanTab struct {
	// 服务层依赖
	taskService   *services.TaskService
	hostService   *services.HostService
	exportService *services.ExportService

	// 基础依赖
	updateStatus func(string)
	window       fyne.Window
	app          *App

	// UI组件 - 使用专门的组件
	content      *fyne.Container
	toolbar      *components.ScanToolbar
	taskTable    *components.ScanTable

	// 当前数据
	selectedTask *data.ScanTask
}

// NewScanTab 创建扫描标签页 - 使用模块化组件
func NewScanTab(storage data.Storage, scanner *core.Scanner, updateStatus func(string)) *ScanTab {
	// 初始化服务层
	taskService := services.NewTaskService(storage, scanner)
	sshClient := scanner.GetSSHClient() // 获取SSH客户端
	hostService := services.NewHostService(storage, sshClient)
	exportService := services.NewExportService(storage)

	tab := &ScanTab{
		taskService:   taskService,
		hostService:   hostService,
		exportService: exportService,
		updateStatus:  updateStatus,
	}

	tab.buildUI()
	tab.setupCallbacks()
	tab.loadTasks()

	return tab
}

// SetWindow 设置窗口引用
func (st *ScanTab) SetWindow(window fyne.Window) {
	st.window = window
}

// SetApp 设置应用引用
func (st *ScanTab) SetApp(app *App) {
	st.app = app
}

// buildUI 构建UI - 使用专门的组件
func (st *ScanTab) buildUI() {
	// 创建专门的UI组件
	st.toolbar = components.NewScanToolbar()
	st.taskTable = components.NewScanTable()

	// 创建主布局 - 移除底部进度显示
	st.content = container.NewBorder(
		st.toolbar.GetContainer(), // 顶部工具栏
		nil,                       // 底部为空
		nil, nil,                  // 左右为空
		st.taskTable.GetContainer(), // 中间任务列表
	)
}

// setupCallbacks 设置回调函数 - 连接组件和业务逻辑
func (st *ScanTab) setupCallbacks() {
	// 工具栏回调
	st.toolbar.SetOnCreateTask(st.handleCreateTask)
	st.toolbar.SetOnStartTask(st.handleStartTask)
	st.toolbar.SetOnStopTask(st.handleStopTask)
	st.toolbar.SetOnRestartTask(st.handleRestartTask)
	st.toolbar.SetOnShowDetail(st.handleShowDetail)
	st.toolbar.SetOnDeleteTask(st.handleDeleteTask)
	st.toolbar.SetOnExportTask(st.handleExportTask)
	st.toolbar.SetOnRefreshData(st.handleRefreshData)

	// 任务表格回调
	st.taskTable.SetOnSelection(st.handleTaskSelection)
	st.taskTable.SetOnDoubleClick(st.handleTaskDoubleClick)

	// 移除进度组件回调
}

// GetContent 获取内容容器
func (st *ScanTab) GetContent() *fyne.Container {
	return st.content
}

// ========== 数据操作方法 ==========

// loadTasks 加载任务列表 - 委托给任务服务
func (st *ScanTab) loadTasks() {
	tasks, err := st.taskService.LoadTasks()
	if err != nil {
		st.showError("加载任务失败", err)
		return
	}

	// 按创建时间正序排序，最早创建的任务在最上面
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].CreatedAt.Before(tasks[j].CreatedAt)
	})

	st.taskTable.SetTasks(tasks)

	// 更新工具栏按钮状态
	st.toolbar.SetSelectedTaskWithTasks(st.selectedTask, tasks)

	// 任务数量信息现在直接体现在任务列表中
	if len(tasks) == 0 {
		st.updateStatus("暂无任务")
		st.selectedTask = nil // 清除选中任务
	} else {
		st.updateStatus("就绪")
	}
}

// refreshTasks 刷新任务列表
func (st *ScanTab) refreshTasks() {
	st.loadTasks()
}

// RefreshData 刷新数据（公共方法）
func (st *ScanTab) RefreshData() {
	st.refreshTasks()
}

// ========== 事件处理方法 ==========

// handleCreateTask 处理创建任务
func (st *ScanTab) handleCreateTask() {
	st.showCreateTaskDialog()
}

// handleDeleteTask 处理删除任务
func (st *ScanTab) handleDeleteTask() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要删除的任务")
		return
	}

	st.showDeleteConfirmDialog()
}

// handleStartTask 处理启动任务
func (st *ScanTab) handleStartTask() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要启动的任务")
		return
	}

	if st.selectedTask.Status != "待执行" {
		st.showInfo("提示", "只能启动待执行状态的任务")
		return
	}

	st.startTask(st.selectedTask)
}

// handleStopTask 处理停止任务
func (st *ScanTab) handleStopTask() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要停止的任务")
		return
	}

	if st.selectedTask.Status != "运行中" {
		st.showInfo("提示", "只能停止运行中的任务")
		return
	}

	st.stopTask(st.selectedTask)
}

// handleRestartTask 处理重启任务
func (st *ScanTab) handleRestartTask() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要重启的任务")
		return
	}

	// 检查任务状态
	if st.selectedTask.Status != "已完成" && st.selectedTask.Status != "失败" &&
	   st.selectedTask.Status != "已停止" && st.selectedTask.Status != "部分成功" {
		st.showInfo("提示", "只能重启已完成、失败、已停止或部分成功的任务")
		return
	}

	// 确认对话框
	dialog.ShowConfirm("确认重启", fmt.Sprintf("确定要重启任务 '%s' 吗？\n\n重启将清空之前的扫描结果并重新开始扫描。", st.selectedTask.Name), func(confirmed bool) {
		if confirmed {
			st.updateStatus(fmt.Sprintf("正在重启任务 %s...", st.selectedTask.Name))

			// 在goroutine中执行重启操作，避免阻塞UI
			go func() {
				err := st.taskService.RestartTask(st.selectedTask)
				if err != nil {
					st.showError("重启任务失败", err)
					st.updateStatus("重启任务失败")
					return
				}

				st.updateStatus(fmt.Sprintf("任务 %s 已重启", st.selectedTask.Name))
				st.refreshTasks()

				// 开始监控任务进度
				st.startProgressMonitoring(st.selectedTask)
			}()
		}
	}, st.window)
}

// handleShowDetail 处理显示详情
func (st *ScanTab) handleShowDetail() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要查看详情的任务")
		return
	}

	st.showTaskDetails(st.selectedTask)
}

// handleExportTask 处理导出任务
func (st *ScanTab) handleExportTask() {
	// 获取所有已完成的任务
	tasks, err := st.taskService.LoadTasks()
	if err != nil {
		st.showError("加载任务失败", err)
		return
	}

	var completedTasks []data.ScanTask
	for _, task := range tasks {
		if task.Status == "已完成" || task.Status == "失败" || task.Status == "部分成功" {
			completedTasks = append(completedTasks, task)
		}
	}

	if len(completedTasks) == 0 {
		st.showInfo("提示", "没有可导出的任务\n只能导出已完成、失败或部分成功的任务")
		return
	}

	// 如果只有一个可导出的任务，直接导出HTML
	if len(completedTasks) == 1 {
		st.exportTaskAsHTML(&completedTasks[0])
		return
	}

	// 如果有选中的任务且可导出，导出选中的任务
	if st.selectedTask != nil {
		for _, task := range completedTasks {
			if task.ID == st.selectedTask.ID {
				st.exportTaskAsHTML(st.selectedTask)
				return
			}
		}
	}

	// 多个任务时显示选择对话框
	st.showExportTaskSelectionDialog(completedTasks)
}

// handleRefreshData 处理刷新数据
func (st *ScanTab) handleRefreshData() {
	st.refreshTasks()
}

// handleTaskSelection 处理任务选择
func (st *ScanTab) handleTaskSelection(task *data.ScanTask) {
	st.selectedTask = task

	// 获取所有任务以更新按钮状态
	tasks, _ := st.taskService.LoadTasks()

	// 添加调试信息
	if task != nil {
		fmt.Printf("任务选中事件 - 任务: %s, 状态: %s\n", task.Name, task.Status)
	} else {
		fmt.Printf("任务选中事件 - 取消选择\n")
	}

	st.toolbar.SetSelectedTaskWithTasks(task, tasks)

	if task != nil {
		st.updateStatus(fmt.Sprintf("已选择任务: %s", task.Name))
	} else {
		st.updateStatus("未选择任务")
	}
}

// handleTaskDoubleClick 处理任务双击
func (st *ScanTab) handleTaskDoubleClick(task *data.ScanTask) {
	if task == nil {
		return
	}

	// 双击显示任务详情
	st.showTaskDetails(task)
}

// handleProgressUpdate 处理进度更新 - 已移除进度显示
func (st *ScanTab) handleProgressUpdate(progress float64) {
	// 进度信息现在直接在任务项中显示，无需额外处理
}

// ========== 业务逻辑方法 ==========

// startTask 启动任务
func (st *ScanTab) startTask(task *data.ScanTask) {
	st.updateStatus("正在启动任务...")

	// 委托给任务服务执行
	go func() {
		err := st.taskService.StartTask(task)
		if err != nil {
			st.showError("启动任务失败", err)
			st.updateStatus("启动失败")
		} else {
			st.updateStatus("任务已启动")
			st.refreshTasks()

			// 启动进度监控
			st.startProgressMonitoring(task)
		}
	}()
}

// startProgressMonitoring 启动进度监控
func (st *ScanTab) startProgressMonitoring(task *data.ScanTask) {
	go func() {
		ticker := time.NewTicker(2 * time.Second) // 每2秒刷新一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// 重新加载任务状态
				tasks, err := st.taskService.LoadTasks()
				if err != nil {
					continue
				}

				// 查找当前任务
				var currentTask *data.ScanTask
				for i := range tasks {
					if tasks[i].ID == task.ID {
						currentTask = &tasks[i]
						break
					}
				}

				if currentTask == nil {
					return
				}

				// 更新UI
				st.taskTable.SetTasks(tasks)

				// 如果任务完成或失败，停止监控并刷新列表
				if currentTask.Status == "已完成" || currentTask.Status == "失败" || currentTask.Status == "已停止" || currentTask.Status == "部分成功" {
					// 最后一次刷新任务列表，确保显示最终状态
					st.taskTable.SetTasks(tasks)

					// 显示详细的完成信息
					statusMsg := fmt.Sprintf("任务 %s %s", currentTask.Name, currentTask.Status)
					if currentTask.Error != "" {
						statusMsg += fmt.Sprintf(" - %s", currentTask.Error)
					}
					st.updateStatus(statusMsg)

					// 延迟一小段时间后再次刷新，确保UI完全更新
					go func() {
						time.Sleep(500 * time.Millisecond)
						st.refreshTasks()
					}()

					return
				}

				// 更新状态信息
				if currentTask.Status == "运行中" {
					st.updateStatus(fmt.Sprintf("任务 %s 运行中 (%.1f%%)", currentTask.Name, currentTask.Progress*100))
				}
			}
		}
	}()
}

// stopTask 停止任务
func (st *ScanTab) stopTask(task *data.ScanTask) {
	err := st.taskService.StopTask(task)
	if err != nil {
		st.showError("停止任务失败", err)
		return
	}

	st.refreshTasks()
	st.updateStatus("任务已停止")
}

// exportTaskAsHTML 导出任务为HTML格式
func (st *ScanTab) exportTaskAsHTML(task *data.ScanTask) {
	st.updateStatus("正在生成HTML安全检查报告...")

	// 检查是否为主机组任务
	isGroup, group := st.exportService.IsGroupTask(task)

	if isGroup && group != nil {
		// 主机组任务：创建文件夹，每个主机单独报告
		st.updateStatus(fmt.Sprintf("正在为主机组 '%s' 生成报告...", group.Name))

		err := st.exportService.ExportGroupTaskAsHTML(task, group)
		if err != nil {
			st.showError("生成主机组报告失败", err)
			st.updateStatus("导出失败")
			return
		}

		// 生成成功消息
		timestamp := time.Now().Format("20060102_150405")
		groupFolderName := fmt.Sprintf("%s_%s",
			strings.ReplaceAll(group.Name, " ", "_"), timestamp)
		folderPath := fmt.Sprintf("./exports/%s", groupFolderName)

		successMsg := fmt.Sprintf("主机组 '%s' 的安全检查报告已导出到:\n%s\n\n包含 %d 个主机的独立报告文件，每个主机一个HTML文件。",
			group.Name, folderPath, len(task.Results))
		st.showInfo("主机组报告导出成功", successMsg)
		st.updateStatus("主机组报告导出完成")
	} else {
		// 单主机或混合任务：生成单个报告文件
		content, err := st.exportService.ExportTaskAsSecurityReport(task)
		if err != nil {
			st.showError("生成报告失败", err)
			st.updateStatus("导出失败")
			return
		}

		// 生成文件名
		fileName := st.generateSecurityReportFileName(task)
		filePath := "./exports/" + fileName

		// 保存文件
		err = st.exportService.SaveExportFile(filePath, content)
		if err != nil {
			st.showError("保存报告文件失败", err)
			st.updateStatus("保存文件失败")
			return
		}

		st.showInfo("报告导出成功", fmt.Sprintf("Linux安全策略核查报告已导出到:\n%s\n\n报告包含完整的系统信息、安全配置检查结果和详细的核查数据。", filePath))
		st.updateStatus("报告导出完成")
	}
}

// generateSecurityReportFileName 生成安全报告文件名
func (st *ScanTab) generateSecurityReportFileName(task *data.ScanTask) string {
	timestamp := time.Now().Format("20060102_150405")
	// 如果任务只有一个主机，使用主机名
	if len(task.Results) == 1 {
		hostName := strings.ReplaceAll(task.Results[0].HostName, " ", "_")
		hostIP := strings.ReplaceAll(task.Results[0].Host, ".", "_")
		return fmt.Sprintf("%s_%s_Linux安全策略核查_%s.html", hostName, hostIP, timestamp)
	}
	// 多个主机使用任务名
	taskName := strings.ReplaceAll(task.Name, " ", "_")
	return fmt.Sprintf("%s_Linux安全策略核查_%s.html", taskName, timestamp)
}

// showExportTaskSelectionDialog 显示导出任务选择对话框
func (st *ScanTab) showExportTaskSelectionDialog(tasks []data.ScanTask) {
	// 创建任务选择列表
	var taskNames []string
	for _, task := range tasks {
		status := task.Status
		if task.Status == "已完成" {
			status = "✅ 已完成"
		} else if task.Status == "失败" {
			status = "❌ 失败"
		} else if task.Status == "部分成功" {
			status = "⚠️ 部分成功"
		}

		// 显示任务名和主机数量
		hostCount := len(task.Results)
		taskNames = append(taskNames, fmt.Sprintf("%s (%s) - %d台主机", task.Name, status, hostCount))
	}

	// 创建选择列表
	taskList := widget.NewList(
		func() int { return len(taskNames) },
		func() fyne.CanvasObject {
			return widget.NewLabel("任务名称")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			obj.(*widget.Label).SetText(taskNames[id])
		},
	)
	taskList.Resize(fyne.NewSize(450, 250))

	var selectedTaskIndex = -1
	taskList.OnSelected = func(id widget.ListItemID) {
		selectedTaskIndex = id
	}

	// 创建对话框内容
	content := container.NewVBox(
		widget.NewLabel("选择要导出Linux安全策略核查报告的任务:"),
		taskList,
		widget.NewSeparator(),
		widget.NewLabel("将生成专业的HTML格式安全检查报告，包含完整的系统信息和核查结果。"),
	)

	// 创建对话框
	exportDialog := dialog.NewCustomConfirm("导出安全检查报告", "导出HTML报告", "取消", content,
		func(confirmed bool) {
			if !confirmed || selectedTaskIndex < 0 {
				return
			}

			// 获取选中的任务并导出
			selectedTask := &tasks[selectedTaskIndex]
			st.exportTaskAsHTML(selectedTask)
		}, st.window)

	exportDialog.Resize(fyne.NewSize(500, 400))
	exportDialog.Show()
}

// ========== 对话框方法 ==========

// showCreateTaskDialog 显示创建任务对话框
func (st *ScanTab) showCreateTaskDialog() {
	fmt.Println("=== 开始创建任务对话框 ===")

	// 检查window是否已设置
	if st.window == nil {
		fmt.Println("错误：窗口未初始化")
		st.updateStatus("错误：窗口未初始化")
		return
	}
	fmt.Println("✓ Window 已初始化")

	// 检查服务是否已初始化
	if st.hostService == nil {
		fmt.Println("错误：hostService 未初始化")
		st.updateStatus("错误：主机服务未初始化")
		return
	}
	fmt.Println("✓ HostService 已初始化")

	if st.taskService == nil {
		fmt.Println("错误：taskService 未初始化")
		st.updateStatus("错误：任务服务未初始化")
		return
	}
	fmt.Println("✓ TaskService 已初始化")

	fmt.Println("准备调用 ShowCreateTaskDialog...")

	// 使用 defer 来捕获可能的 panic
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("捕获到 panic: %v\n", r)
			st.updateStatus(fmt.Sprintf("创建任务对话框出错: %v", r))
		}
	}()

	// 使用简化的创建任务对话框
	st.showSimpleCreateDialog()

	fmt.Println("ShowCreateTaskDialog 调用完成")
}

// showSimpleCreateDialog 显示创建任务对话框
func (st *ScanTab) showSimpleCreateDialog() {
	fmt.Println("🚀 显示创建任务对话框")

	// 立即测试数据库连接
	fmt.Println("=== 立即测试数据库连接 ===")
	testHosts, err := st.hostService.LoadHosts()
	if err != nil {
		fmt.Printf("❌ 对话框打开时测试加载主机失败: %v\n", err)
		st.showError("数据库连接失败", fmt.Errorf("无法加载主机数据: %v", err))
		return
	} else {
		fmt.Printf("✅ 对话框打开时测试成功，数据库中有 %d 个主机:\n", len(testHosts))
		for i, host := range testHosts {
			fmt.Printf("  %d. %s (%s:%s) - %s\n", i+1, host.Name, host.Host, host.Port, host.Username)
		}
	}

	testGroups, err := st.hostService.LoadGroups()
	if err != nil {
		fmt.Printf("❌ 对话框打开时测试加载主机组失败: %v\n", err)
	} else {
		fmt.Printf("✅ 对话框打开时测试成功，数据库中有 %d 个主机组:\n", len(testGroups))
		for i, group := range testGroups {
			fmt.Printf("  %d. %s (%d个主机) - %s\n", i+1, group.Name, len(group.Hosts), group.Description)
		}
	}

	// 任务名称输入
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("请输入任务名称")

	// 模式选择
	modeSelect := widget.NewRadioGroup([]string{"主机模式", "主机组"}, nil)
	modeSelect.SetSelected("主机模式")

	// 主机选择（简化版）
	var concurrencyEntry *widget.Entry
	var selectedHosts []string
	var selectedGroups []string

	// 动态内容容器 - 使用合适的大小
	dynamicContent := container.NewVBox()
	dynamicContent.Resize(fyne.NewSize(350, 180))

	// 加载主机数据 - 每次打开对话框都重新加载
	fmt.Println("=== 创建任务对话框：开始加载数据 ===")

	var hosts []data.HostInfo
	var groups []data.HostGroup

	// 显示主机列表的函数
	showHostList := func() {
		fmt.Println("=== 显示主机列表 ===")
		dynamicContent.RemoveAll()

		// 每次都重新从数据库加载最新数据
		fmt.Println("正在从数据库重新加载主机数据...")
		freshHosts, err := st.hostService.LoadHosts()
		if err != nil {
			fmt.Printf("❌ 重新加载主机失败: %v\n", err)
			errorLabel := widget.NewLabel("加载主机失败: " + err.Error())
			errorLabel.Importance = widget.DangerImportance
			dynamicContent.Add(errorLabel)
			dynamicContent.Refresh()
			return
		}

		hosts = freshHosts // 更新hosts变量
		fmt.Printf("✅ 成功重新加载了 %d 个主机\n", len(hosts))

		// 详细打印每个主机信息
		for i, host := range hosts {
			fmt.Printf("  主机%d: ID=%s, 名称=%s, 地址=%s:%s, 用户=%s\n",
				i+1, host.ID, host.Name, host.Host, host.Port, host.Username)
		}

		if len(hosts) == 0 {
			fmt.Println("⚠️  没有主机数据，显示提示信息")
			emptyLabel := widget.NewLabel("暂无主机数据\n请先在主机管理页面添加主机")
			emptyLabel.Alignment = fyne.TextAlignCenter
			emptyLabel.Importance = widget.MediumImportance

			dynamicContent.RemoveAll()
			dynamicContent.Add(emptyLabel)
			dynamicContent.Refresh()
			return
		}

		// 创建主机选择列表
		fmt.Println("创建主机选择列表...")
		hostNames := make([]string, len(hosts))
		for i, host := range hosts {
			hostNames[i] = fmt.Sprintf("%s (%s:%s)", host.Name, host.Host, host.Port)
			fmt.Printf("  选项%d: %s\n", i+1, hostNames[i])
		}

		// 先清空容器
		dynamicContent.RemoveAll()

		// 添加一个简单的标签显示主机数量
		countLabel := widget.NewLabel(fmt.Sprintf("找到 %d 个主机:", len(hosts)))
		countLabel.Importance = widget.HighImportance
		dynamicContent.Add(countLabel)

		hostCheck := widget.NewCheckGroup(hostNames, func(selected []string) {
			fmt.Printf("🔄 主机选择变更: %v\n", selected)
			selectedHosts = make([]string, 0)
			for i, name := range hostNames {
				for _, sel := range selected {
					if sel == name {
						selectedHosts = append(selectedHosts, hosts[i].ID)
						fmt.Printf("  ✅ 选中主机: %s (ID: %s)\n", hosts[i].Name, hosts[i].ID)
						break
					}
				}
			}
			fmt.Printf("📋 当前选中的主机ID: %v\n", selectedHosts)
		})

		fmt.Println("创建滚动容器...")
		scrollContainer := container.NewScroll(hostCheck)
		scrollContainer.SetMinSize(fyne.NewSize(350, 150))

		fmt.Println("添加滚动容器到动态容器...")
		dynamicContent.Add(scrollContainer)

		fmt.Println("刷新动态容器...")
		dynamicContent.Refresh()

		fmt.Println("✅ 主机列表显示完成")
	}

	// 显示主机组列表的函数
	showGroupList := func() {
		fmt.Println("=== 显示主机组列表 ===")
		dynamicContent.RemoveAll()

		// 每次都重新从数据库加载最新数据
		fmt.Println("正在从数据库重新加载主机组数据...")
		freshGroups, err := st.hostService.LoadGroups()
		if err != nil {
			fmt.Printf("❌ 重新加载主机组失败: %v\n", err)
			errorLabel := widget.NewLabel("加载主机组失败: " + err.Error())
			errorLabel.Importance = widget.DangerImportance
			dynamicContent.Add(errorLabel)
			dynamicContent.Refresh()
			return
		}

		groups = freshGroups // 更新groups变量
		fmt.Printf("✅ 成功重新加载了 %d 个主机组\n", len(groups))

		// 详细打印每个主机组信息
		for i, group := range groups {
			fmt.Printf("  主机组%d: ID=%s, 名称=%s, 描述=%s, 包含%d个主机\n",
				i+1, group.ID, group.Name, group.Description, len(group.Hosts))
			for j, host := range group.Hosts {
				fmt.Printf("    主机%d: %s (%s:%s)\n", j+1, host.Name, host.Host, host.Port)
			}
		}

		if len(groups) == 0 {
			fmt.Println("⚠️  没有主机组数据，显示提示信息")
			emptyLabel := widget.NewLabel("暂无主机组数据\n请先在主机管理页面添加主机组")
			emptyLabel.Alignment = fyne.TextAlignCenter
			emptyLabel.Importance = widget.MediumImportance
			dynamicContent.RemoveAll()
			dynamicContent.Add(emptyLabel)
			dynamicContent.Refresh()
			return
		}

		// 创建主机组选择列表
		fmt.Println("创建主机组选择列表...")
		groupNames := make([]string, len(groups))
		for i, group := range groups {
			groupNames[i] = fmt.Sprintf("%s (%d个主机)", group.Name, len(group.Hosts))
			fmt.Printf("  选项%d: %s\n", i+1, groupNames[i])
		}

		groupCheck := widget.NewCheckGroup(groupNames, func(selected []string) {
			fmt.Printf("🔄 主机组选择变更: %v\n", selected)
			selectedGroups = make([]string, 0)
			for i, name := range groupNames {
				for _, sel := range selected {
					if sel == name {
						selectedGroups = append(selectedGroups, groups[i].ID)
						fmt.Printf("  ✅ 选中主机组: %s (ID: %s)\n", groups[i].Name, groups[i].ID)
						break
					}
				}
			}
			fmt.Printf("📋 当前选中的主机组ID: %v\n", selectedGroups)
		})

		// 并发设置
		concurrencyEntry = widget.NewEntry()
		concurrencyEntry.SetText("5")
		concurrencyEntry.SetPlaceHolder("并发数")

		scrollContainer := container.NewScroll(groupCheck)
		scrollContainer.SetMinSize(fyne.NewSize(350, 120))

		dynamicContent.Add(scrollContainer)
		dynamicContent.Add(widget.NewSeparator())
		dynamicContent.Add(container.NewBorder(nil, nil,
			widget.NewLabel("选择主机组才有并发设置:"), nil, concurrencyEntry))
		dynamicContent.Refresh()
		fmt.Println("✅ 主机组列表显示完成")
	}

	// 设置模式切换回调
	modeSelect.OnChanged = func(selected string) {
		fmt.Printf("模式切换到: %s\n", selected)
		if selected == "主机模式" {
			showHostList()
		} else if selected == "主机组" {
			showGroupList()
		}
	}

	// 立即加载主机数据并创建静态列表
	fmt.Println("🚀 对话框初始化：立即加载主机数据")
	currentHosts, err := st.hostService.LoadHosts()
	if err != nil {
		fmt.Printf("❌ 初始化时加载主机失败: %v\n", err)
		currentHosts = make([]data.HostInfo, 0)
	} else {
		fmt.Printf("✅ 初始化时成功加载 %d 个主机\n", len(currentHosts))
		for i, host := range currentHosts {
			fmt.Printf("  %d. %s (%s:%s)\n", i+1, host.Name, host.Host, host.Port)
		}
	}

	// 创建静态主机列表
	var staticHostList *widget.CheckGroup
	if len(currentHosts) > 0 {
		hostNames := make([]string, len(currentHosts))
		for i, host := range currentHosts {
			hostNames[i] = fmt.Sprintf("%s (%s:%s)", host.Name, host.Host, host.Port)
		}

		staticHostList = widget.NewCheckGroup(hostNames, func(selected []string) {
			fmt.Printf("🔄 静态主机选择变更: %v\n", selected)
			selectedHosts = make([]string, 0)
			for i, name := range hostNames {
				for _, sel := range selected {
					if sel == name {
						selectedHosts = append(selectedHosts, currentHosts[i].ID)
						fmt.Printf("  ✅ 选中主机: %s (ID: %s)\n", currentHosts[i].Name, currentHosts[i].ID)
						break
					}
				}
			}
			fmt.Printf("📋 当前选中的主机ID: %v\n", selectedHosts)
		})

		// 立即添加到动态容器
		dynamicContent.Add(widget.NewLabel(fmt.Sprintf("找到 %d 个主机:", len(currentHosts))))

		// 为静态主机列表也添加滚动容器
		staticScrollContainer := container.NewScroll(staticHostList)
		staticScrollContainer.SetMinSize(fyne.NewSize(350, 150))
		dynamicContent.Add(staticScrollContainer)
		fmt.Println("✅ 静态主机列表已添加到容器")
	} else {
		emptyLabel := widget.NewLabel("暂无主机数据\n请先在主机管理页面添加主机")
		emptyLabel.Alignment = fyne.TextAlignCenter
		emptyLabel.Importance = widget.MediumImportance
		dynamicContent.Add(emptyLabel)
		fmt.Println("⚠️  没有主机数据，显示提示信息")
	}

	// 添加一个刷新按钮来重新加载数据
	refreshButton := widget.NewButton("刷新数据", func() {
		fmt.Println("🔄 手动刷新数据...")
		if modeSelect.Selected == "主机模式" {
			showHostList()
		} else {
			showGroupList()
		}
	})



	// 创建表单内容
	formContent := container.NewVBox(
		// 任务名称
		container.NewBorder(nil, nil, widget.NewLabel("任务名称:"), nil, nameEntry),

		// 模式选择
		container.NewBorder(nil, nil, widget.NewLabel("选择模式:"), nil, modeSelect),

		// 动态内容区域
		widget.NewLabel("主机列表/主机组列表:"),
		dynamicContent,

		// 刷新按钮
		refreshButton,

		// 添加一些间距，让按钮下移
		widget.NewSeparator(),
		widget.NewLabel(""), // 空标签作为间距
	)

	// 创建并显示对话框（使用NewCustomConfirm，与添加主机对话框一致）
	createDialog := dialog.NewCustomConfirm("创建任务", "创建", "取消", formContent, func(confirmed bool) {
		if !confirmed {
			return
		}

		// 验证输入
		taskName := nameEntry.Text
		if taskName == "" {
			st.showInfo("提示", "请输入任务名称")
			return
		}

		var hostIDs []string
		concurrency := 1

		if modeSelect.Selected == "主机模式" {
			if len(selectedHosts) == 0 {
				st.showInfo("提示", "请至少选择一个主机")
				return
			}
			hostIDs = selectedHosts
		} else {
			if len(selectedGroups) == 0 {
				st.showInfo("提示", "请至少选择一个主机组")
				return
			}

			// 获取主机组中的所有主机
			for _, groupID := range selectedGroups {
				for _, group := range groups {
					if group.ID == groupID {
						for _, host := range group.Hosts {
							hostIDs = append(hostIDs, host.ID)
						}
						break
					}
				}
			}

			// 解析并发数
			if concurrencyEntry != nil && concurrencyEntry.Text != "" {
				if c, err := fmt.Sscanf(concurrencyEntry.Text, "%d", &concurrency); err != nil || c != 1 || concurrency < 1 {
					st.showInfo("提示", "并发数必须是大于0的整数")
					return
				}
			}
		}

		if len(hostIDs) == 0 {
			st.showInfo("提示", "没有找到可用的主机")
			return
		}

		// 创建任务
		task, err := st.taskService.CreateTask(taskName, hostIDs, concurrency)
		if err != nil {
			st.showError("创建任务失败", err)
			return
		}

		// 保存任务
		err = st.taskService.SaveTask(task)
		if err != nil {
			st.showError("保存任务失败", err)
			return
		}

		st.refreshTasks()
		st.updateStatus(fmt.Sprintf("任务 '%s' 创建成功", task.Name))
	}, st.window)

	createDialog.Resize(fyne.NewSize(400, 450))
	createDialog.Show()

	fmt.Println("创建任务对话框显示完成")
}

// showDeleteConfirmDialog 显示删除确认对话框
func (st *ScanTab) showDeleteConfirmDialog() {
	dialog.ShowConfirm("确认删除",
		fmt.Sprintf("确定要删除任务 '%s' 吗？", st.selectedTask.Name),
		func(confirmed bool) {
			if confirmed {
				st.deleteTask(st.selectedTask)
			}
		}, st.window)
}

// showTaskDetails 显示任务详情 - 简化版本，只显示统计信息，实时刷新
func (st *ScanTab) showTaskDetails(task *data.ScanTask) {
	// 重新加载任务以获取最新状态
	tasks, err := st.taskService.LoadTasks()
	if err != nil {
		st.showError("加载任务失败", err)
		return
	}

	// 查找当前任务的最新状态
	var currentTask *data.ScanTask
	for i := range tasks {
		if tasks[i].ID == task.ID {
			currentTask = &tasks[i]
			break
		}
	}

	if currentTask == nil {
		st.showError("任务不存在", fmt.Errorf("任务 %s 不存在", task.Name))
		return
	}

	// 获取实际的主机数量
	taskHosts, err := st.taskService.GetTaskHosts(currentTask.HostIDs)
	actualHostCount := len(currentTask.HostIDs) // 默认使用HostIDs数量
	if err == nil {
		actualHostCount = len(taskHosts) // 如果能获取到实际主机，使用实际数量
	}

	// 使用最新的任务状态构建基本信息
	details := fmt.Sprintf("任务名称: %s\n状态: %s\n进度: %.1f%%\n主机数量: %d\n",
		currentTask.Name, currentTask.Status, currentTask.Progress*100, actualHostCount)

	// 添加时间信息
	details += fmt.Sprintf("创建时间: %s\n", currentTask.CreatedAt.Format("2006-01-02 15:04:05"))
	if currentTask.StartedAt != nil {
		details += fmt.Sprintf("开始时间: %s\n", currentTask.StartedAt.Format("2006-01-02 15:04:05"))
	}
	if currentTask.CompletedAt != nil {
		details += fmt.Sprintf("完成时间: %s\n", currentTask.CompletedAt.Format("2006-01-02 15:04:05"))
	}

	// 添加错误信息
	if currentTask.Error != "" {
		details += fmt.Sprintf("错误信息: %s\n", currentTask.Error)
	}

	// 添加扫描结果统计 - 简化版本
	if len(currentTask.Results) > 0 {
		successCount := 0
		failureCount := 0
		totalScore := 0
		totalMaxScore := 0
		totalPassedChecks := 0
		totalFailedChecks := 0
		totalWarningChecks := 0

		details += "\n=== 扫描结果统计 ===\n"

		for _, result := range currentTask.Results {
			if result.Status == "成功" {
				successCount++
				totalScore += result.TotalScore
				totalMaxScore += result.MaxScore
			} else {
				failureCount++
			}

			// 累计检查项统计
			totalPassedChecks += result.PassedChecks
			totalFailedChecks += result.FailedChecks
			totalWarningChecks += result.WarningChecks
		}

		// 显示主机统计
		details += fmt.Sprintf("扫描成功: %d 台主机\n", successCount)
		details += fmt.Sprintf("扫描失败: %d 台主机\n", failureCount)

		// 显示检查项统计
		if totalPassedChecks > 0 || totalFailedChecks > 0 || totalWarningChecks > 0 {
			details += fmt.Sprintf("\n检查项统计:\n")
			details += fmt.Sprintf("✅ 通过: %d 项\n", totalPassedChecks)
			details += fmt.Sprintf("❌ 失败: %d 项\n", totalFailedChecks)
			details += fmt.Sprintf("⚠️  警告: %d 项\n", totalWarningChecks)
		}

		// 显示平均得分
		if successCount > 0 && totalMaxScore > 0 {
			avgScore := totalScore / successCount
			avgMaxScore := totalMaxScore / successCount
			details += fmt.Sprintf("\n平均得分: %d/%d (%.1f%%)\n", avgScore, avgMaxScore, float64(avgScore)/float64(avgMaxScore)*100)
		}
	}

	// 如果任务正在运行，显示实时刷新的详情对话框
	if currentTask.Status == "运行中" {
		st.showLiveTaskDetails(currentTask)
	} else {
		st.showInfo("任务详情", details)
	}
}

// showLiveTaskDetails 显示实时刷新的任务详情对话框
func (st *ScanTab) showLiveTaskDetails(task *data.ScanTask) {
	// 创建详情标签
	detailsLabel := widget.NewLabel("")
	detailsLabel.Wrapping = fyne.TextWrapWord

	// 创建滚动容器
	scroll := container.NewScroll(detailsLabel)
	scroll.SetMinSize(fyne.NewSize(500, 300))

	// 创建关闭按钮
	closeButton := widget.NewButton("关闭", nil)

	// 创建对话框内容
	content := container.NewVBox(
		scroll,
		widget.NewSeparator(),
		container.NewCenter(closeButton),
	)

	// 创建对话框
	detailDialog := dialog.NewCustom("任务详情 (实时)", "", content, st.window)
	detailDialog.Resize(fyne.NewSize(600, 400))

	// 设置关闭按钮回调
	closeButton.OnTapped = func() {
		detailDialog.Hide()
	}

	// 更新详情的函数
	updateDetails := func() {
		// 重新加载任务状态
		tasks, err := st.taskService.LoadTasks()
		if err != nil {
			detailsLabel.SetText(fmt.Sprintf("加载任务失败: %v", err))
			return
		}

		// 查找当前任务
		var currentTask *data.ScanTask
		for i := range tasks {
			if tasks[i].ID == task.ID {
				currentTask = &tasks[i]
				break
			}
		}

		if currentTask == nil {
			detailsLabel.SetText("任务不存在")
			return
		}

		// 获取实际的主机数量
		taskHosts, err := st.taskService.GetTaskHosts(currentTask.HostIDs)
		actualHostCount := len(currentTask.HostIDs) // 默认使用HostIDs数量
		if err == nil {
			actualHostCount = len(taskHosts) // 如果能获取到实际主机，使用实际数量
		}

		// 构建详情文本
		details := fmt.Sprintf("任务名称: %s\n状态: %s\n进度: %.1f%%\n主机数量: %d\n",
			currentTask.Name, currentTask.Status, currentTask.Progress*100, actualHostCount)

		// 添加时间信息
		details += fmt.Sprintf("创建时间: %s\n", currentTask.CreatedAt.Format("2006-01-02 15:04:05"))
		if currentTask.StartedAt != nil {
			details += fmt.Sprintf("开始时间: %s\n", currentTask.StartedAt.Format("2006-01-02 15:04:05"))
		}
		if currentTask.CompletedAt != nil {
			details += fmt.Sprintf("完成时间: %s\n", currentTask.CompletedAt.Format("2006-01-02 15:04:05"))
		}

		// 添加错误信息
		if currentTask.Error != "" {
			details += fmt.Sprintf("错误信息: %s\n", currentTask.Error)
		}

		// 添加扫描结果统计
		if len(currentTask.Results) > 0 {
			successCount := 0
			failureCount := 0
			totalScore := 0
			totalMaxScore := 0
			totalPassedChecks := 0
			totalFailedChecks := 0
			totalWarningChecks := 0

			details += "\n=== 扫描结果统计 ===\n"

			for _, result := range currentTask.Results {
				if result.Status == "成功" {
					successCount++
					totalScore += result.TotalScore
					totalMaxScore += result.MaxScore
				} else {
					failureCount++
				}

				// 累计检查项统计
				totalPassedChecks += result.PassedChecks
				totalFailedChecks += result.FailedChecks
				totalWarningChecks += result.WarningChecks
			}

			// 显示主机统计
			details += fmt.Sprintf("扫描成功: %d 台主机\n", successCount)
			details += fmt.Sprintf("扫描失败: %d 台主机\n", failureCount)

			// 显示检查项统计
			if totalPassedChecks > 0 || totalFailedChecks > 0 || totalWarningChecks > 0 {
				details += fmt.Sprintf("\n检查项统计:\n")
				details += fmt.Sprintf("✅ 通过: %d 项\n", totalPassedChecks)
				details += fmt.Sprintf("❌ 失败: %d 项\n", totalFailedChecks)
				details += fmt.Sprintf("⚠️  警告: %d 项\n", totalWarningChecks)
			}

			// 显示平均得分
			if successCount > 0 && totalMaxScore > 0 {
				avgScore := totalScore / successCount
				avgMaxScore := totalMaxScore / successCount
				details += fmt.Sprintf("\n平均得分: %d/%d (%.1f%%)\n", avgScore, avgMaxScore, float64(avgScore)/float64(avgMaxScore)*100)
			}
		}

		detailsLabel.SetText(details)

		// 如果任务完成，停止刷新
		if currentTask.Status != "运行中" {
			return
		}
	}

	// 初始更新
	updateDetails()

	// 启动定时刷新（仅当任务运行中时）
	if task.Status == "运行中" {
		ticker := time.NewTicker(2 * time.Second)
		go func() {
			defer ticker.Stop()
			for {
				select {
				case <-ticker.C:
					updateDetails()
				}
			}
		}()
	}

	// 显示对话框
	detailDialog.Show()
}

// deleteTask 删除任务
func (st *ScanTab) deleteTask(task *data.ScanTask) {
	err := st.taskService.DeleteTask(task.ID)
	if err != nil {
		st.showError("删除任务失败", err)
		return
	}

	st.refreshTasks()
	st.selectedTask = nil
	st.toolbar.SetSelectedTask(nil)
	st.updateStatus("任务已删除")
}

// ========== 辅助方法 ==========

// showError 显示错误对话框
func (st *ScanTab) showError(title string, err error) {
	dialog.ShowError(err, st.window)
	st.updateStatus(fmt.Sprintf("错误: %s", err.Error()))
}

// showInfo 显示信息对话框
func (st *ScanTab) showInfo(title, message string) {
	dialog.ShowInformation(title, message, st.window)
}
